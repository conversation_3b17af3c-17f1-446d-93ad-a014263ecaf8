'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Bell, X, Clock, AlertTriangle, CheckCircle, MessageSquare } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';

export interface Notification {
  id: string;
  type: 'new_request' | 'overdue' | 'urgent_escalation' | 'response' | 'clarification';
  title: string;
  message: string;
  requestId?: number;
  requestNumber?: string;
  employeeName?: string;
  priority?: 'عادي' | 'طاريء' | 'طاريء جدا';
  timestamp: Date;
  read: boolean;
  actionRequired?: boolean;
}

interface NotificationSystemProps {
  userId: number;
  userRole: 'user' | 'manager' | 'admin';
  onNotificationClick?: (notification: Notification) => void;
}

export default function NotificationSystem({ 
  userId, 
  userRole, 
  onNotificationClick 
}: NotificationSystemProps) {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  const { toast } = useToast();

  // جلب الإشعارات من الخادم
  const fetchNotifications = useCallback(async () => {
    try {
      const response = await fetch(`/api/notifications?userId=${userId}`);
      if (response.ok) {
        const data = await response.json();
        setNotifications(data);
        setUnreadCount(data.filter((n: Notification) => !n.read).length);
      }
    } catch (error) {
      console.error('خطأ في جلب الإشعارات:', error);
    }
  }, [userId]);

  // تحديث حالة الإشعار كمقروء
  const markAsRead = async (notificationId: string) => {
    try {
      await fetch(`/api/notifications/${notificationId}/read`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' }
      });
      
      setNotifications(prev => 
        prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
      );
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('خطأ في تحديث حالة الإشعار:', error);
    }
  };

  // معالجة النقر على الإشعار
  const handleNotificationClick = (notification: Notification) => {
    if (!notification.read) {
      markAsRead(notification.id);
    }
    
    if (onNotificationClick) {
      onNotificationClick(notification);
    }
    
    setIsOpen(false);
  };

  // إنشاء إشعار جديد
  const createNotification = useCallback((notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {
    const newNotification: Notification = {
      ...notification,
      id: Date.now().toString(),
      timestamp: new Date(),
      read: false
    };

    setNotifications(prev => [newNotification, ...prev]);
    setUnreadCount(prev => prev + 1);

    // إظهار toast للإشعارات المهمة
    if (notification.type === 'urgent_escalation' || notification.actionRequired) {
      toast({
        title: notification.title,
        description: notification.message,
        variant: notification.type === 'urgent_escalation' ? 'destructive' : 'default',
      });
    }
  }, [toast]);

  // التحقق من الإشعارات الجديدة كل 30 ثانية
  useEffect(() => {
    fetchNotifications();
    const interval = setInterval(fetchNotifications, 30000);
    return () => clearInterval(interval);
  }, [fetchNotifications]);

  // أيقونة نوع الإشعار
  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'new_request':
        return <Bell className="h-4 w-4 text-blue-600" />;
      case 'overdue':
        return <Clock className="h-4 w-4 text-orange-600" />;
      case 'urgent_escalation':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'response':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'clarification':
        return <MessageSquare className="h-4 w-4 text-purple-600" />;
      default:
        return <Bell className="h-4 w-4 text-gray-600" />;
    }
  };

  // لون الأولوية
  const getPriorityColor = (priority?: string) => {
    switch (priority) {
      case 'طاريء جدا':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'طاريء':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      default:
        return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  // تنسيق الوقت
  const formatTime = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `منذ ${days} يوم`;
    if (hours > 0) return `منذ ${hours} ساعة`;
    if (minutes > 0) return `منذ ${minutes} دقيقة`;
    return 'الآن';
  };

  return (
    <div className="relative">
      {/* زر الإشعارات */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2"
      >
        <Bell className="h-5 w-5" />
        {unreadCount > 0 && (
          <Badge 
            variant="destructive" 
            className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
          >
            {unreadCount > 99 ? '99+' : unreadCount}
          </Badge>
        )}
      </Button>

      {/* قائمة الإشعارات */}
      {isOpen && (
        <div className="absolute left-0 top-full mt-2 w-96 max-h-96 overflow-y-auto bg-white border border-gray-200 rounded-lg shadow-lg z-50">
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold text-gray-900">الإشعارات</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            {unreadCount > 0 && (
              <p className="text-sm text-gray-600 mt-1">
                {unreadCount} إشعار غير مقروء
              </p>
            )}
          </div>

          <div className="max-h-80 overflow-y-auto">
            {notifications.length === 0 ? (
              <div className="p-4 text-center text-gray-500">
                لا توجد إشعارات
              </div>
            ) : (
              notifications.map((notification) => (
                <Card
                  key={notification.id}
                  className={`m-2 cursor-pointer transition-colors hover:bg-gray-50 ${
                    !notification.read ? 'bg-blue-50 border-blue-200' : ''
                  }`}
                  onClick={() => handleNotificationClick(notification)}
                >
                  <CardContent className="p-3">
                    <div className="flex items-start space-x-3 space-x-reverse">
                      <div className="flex-shrink-0 mt-1">
                        {getNotificationIcon(notification.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {notification.title}
                          </p>
                          {notification.priority && (
                            <Badge className={`text-xs ${getPriorityColor(notification.priority)}`}>
                              {notification.priority}
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                          {notification.message}
                        </p>
                        <div className="flex items-center justify-between mt-2">
                          <p className="text-xs text-gray-500">
                            {formatTime(notification.timestamp)}
                          </p>
                          {notification.requestNumber && (
                            <p className="text-xs text-blue-600 font-medium">
                              {notification.requestNumber}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
}

// Hook لاستخدام نظام الإشعارات
export function useNotifications(userId: number, userRole: string) {
  const [notificationSystem, setNotificationSystem] = useState<{
    createNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void;
  } | null>(null);

  return {
    notificationSystem,
    setNotificationSystem
  };
}
