const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function cleanTestReturns() {
  try {
    console.log('حذف المرتجعات التجريبية...');

    // حذف المرتجعات التجريبية
    const testReturns = await prisma.$queryRaw`
      SELECT id FROM "Return" WHERE "roNumber" LIKE 'RO-%' OR "roNumber" LIKE 'TEST-%'
    `;

    for (const returnRecord of testReturns) {
      // حذف العناصر أولاً
      await prisma.$executeRaw`
        DELETE FROM "return_items" WHERE "returnId" = ${returnRecord.id}
      `;
      
      // ثم حذف المرتجع
      await prisma.$executeRaw`
        DELETE FROM "Return" WHERE id = ${returnRecord.id}
      `;
      
      console.log(`تم حذف المرتجع ${returnRecord.id}`);
    }

    console.log('✅ تم حذف جميع المرتجعات التجريبية');

  } catch (error) {
    console.error('خطأ في حذف المرتجعات:', error);
  } finally {
    await prisma.$disconnect();
  }
}

cleanTestReturns();
