const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkMaintenanceOrders() {
  try {
    console.log('🔍 فحص أوامر الصيانة في قاعدة البيانات...\n');

    // جلب جميع أوامر الصيانة
    const orders = await prisma.maintenanceOrder.findMany({
      select: {
        id: true,
        orderNumber: true,
        date: true,
        status: true,
        items: true
      },
      orderBy: {
        date: 'desc'
      }
    });

    console.log(`📊 عدد أوامر الصيانة في قاعدة البيانات: ${orders.length}\n`);

    orders.forEach(order => {
      console.log(`🔧 أمر ${order.orderNumber} (ID: ${order.id})`);
      console.log(`   التاريخ: ${order.date}`);
      console.log(`   الحالة: ${order.status}`);
      
      // تحليل items
      try {
        const items = order.items ? JSON.parse(order.items) : [];
        console.log(`   عدد الأجهزة: ${items.length}`);
        if (items.length > 0) {
          const deviceIds = items.map(item => item.deviceId || item.id).filter(Boolean);
          console.log(`   الأجهزة: [${deviceIds.join(', ')}]`);
        }
      } catch (error) {
        console.log(`   خطأ في تحليل items: ${error.message}`);
      }
      console.log('');
    });

    // فحص الأجهزة ذات الحالة "مرسل للصيانة"
    console.log('🔍 فحص الأجهزة المرسلة للصيانة...\n');
    
    const devicesInMaintenance = await prisma.device.findMany({
      where: {
        status: 'مرسل للصيانة'
      },
      select: {
        id: true,
        model: true,
        status: true
      }
    });

    console.log(`📱 عدد الأجهزة المرسلة للصيانة: ${devicesInMaintenance.length}\n`);
    
    devicesInMaintenance.forEach(device => {
      console.log(`📱 جهاز ${device.id} - ${device.model} - حالة: ${device.status}`);
      
      // فحص ما إذا كان هذا الجهاز موجود في أي أمر صيانة
      const deviceInOrders = orders.filter(order => {
        try {
          const items = order.items ? JSON.parse(order.items) : [];
          return items.some(item => (item.deviceId || item.id) === device.id);
        } catch {
          return false;
        }
      });
      
      if (deviceInOrders.length > 0) {
        console.log(`   موجود في أوامر: ${deviceInOrders.map(o => o.orderNumber).join(', ')}`);
      } else {
        console.log(`   ❌ غير موجود في أي أمر صيانة!`);
      }
    });

  } catch (error) {
    console.error('خطأ:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkMaintenanceOrders();
