const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testDeletionLogic() {
  try {
    console.log('🔍 محاكاة منطق حذف أمر الصيانة...\n');

    // جلب أمر الصيانة MTRANS-1
    const maintenanceOrder = await prisma.maintenanceOrder.findFirst({
      where: { orderNumber: 'MTRANS-1' },
      select: {
        id: true,
        orderNumber: true,
        date: true,
        items: true
      }
    });

    if (!maintenanceOrder) {
      console.log('❌ لم يتم العثور على أمر الصيانة MTRANS-1');
      return;
    }

    console.log('🔧 أمر الصيانة:');
    console.log(`   الرقم: ${maintenanceOrder.orderNumber}`);
    console.log(`   التاريخ: ${maintenanceOrder.date} (${new Date(maintenanceOrder.date).toISOString()})`);

    // تحويل items من JSON
    let maintenanceItems = [];
    try {
      maintenanceItems = maintenanceOrder.items ? JSON.parse(maintenanceOrder.items) : [];
    } catch (error) {
      console.warn('خطأ في تحويل items:', error);
    }

    const deviceIds = maintenanceItems.map(item => item.deviceId || item.id).filter(Boolean);
    console.log(`   الأجهزة: [${deviceIds.join(', ')}]\n`);

    const orderDate = new Date(maintenanceOrder.date);

    // جلب جميع أوامر التقييم
    const evaluationOrders = await prisma.evaluationOrder.findMany({
      include: {
        items: {
          select: {
            deviceId: true,
            id: true
          }
        }
      }
    });

    console.log(`📊 أوامر التقييم المتوفرة: ${evaluationOrders.length}`);
    
    // فحص كل أمر تقييم
    const relatedEvaluationOrders = evaluationOrders.filter((evalOrder) => {
      try {
        const evalDate = new Date(evalOrder.date);
        console.log(`\n  🔍 فحص أمر تقييم ${evalOrder.orderId || evalOrder.id}:`);
        console.log(`     التاريخ: ${evalDate.toISOString()}`);
        
        // التأكد من صحة التاريخ
        if (isNaN(evalDate.getTime()) || isNaN(orderDate.getTime())) {
          console.log('     ❌ تاريخ غير صالح');
          return false;
        }
        
        // فحص الأجهزة المشتركة
        const evalDeviceIds = evalOrder.items ? evalOrder.items.map(item => item.deviceId || item.id) : [];
        const sharedDevices = deviceIds.filter(deviceId => evalDeviceIds.includes(deviceId));
        
        console.log(`     أجهزة التقييم: [${evalDeviceIds.join(', ')}]`);
        console.log(`     الأجهزة المشتركة: [${sharedDevices.join(', ')}]`);
        console.log(`     عدد الأجهزة المشتركة: ${sharedDevices.length}`);
        
        const hasSharedDevices = sharedDevices.length > 0;
        const isAfter = evalDate > orderDate;
        
        console.log(`     التاريخ لاحق؟ ${isAfter} (${evalDate.getTime()} > ${orderDate.getTime()})`);
        console.log(`     أجهزة مشتركة؟ ${hasSharedDevices}`);
        
        const result = hasSharedDevices && isAfter;
        console.log(`     النتيجة النهائية: ${result ? 'يمنع الحذف' : 'لا يمنع'}`);
        
        return result;
      } catch (error) {
        console.warn('Error:', error);
        return false;
      }
    });
    
    console.log(`\n📝 النتيجة النهائية:`);
    if (relatedEvaluationOrders.length > 0) {
      const evalOrderNumbers = relatedEvaluationOrders.map(o => o.orderId || o.id).join(', ');
      console.log(`❌ منع الحذف بسبب ${relatedEvaluationOrders.length} أمر تقييم لاحق: ${evalOrderNumbers}`);
    } else {
      console.log('✅ يمكن حذف أمر الصيانة - لا توجد أوامر تقييم لاحقة مرتبطة');
    }

  } catch (error) {
    console.error('خطأ:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testDeletionLogic();
