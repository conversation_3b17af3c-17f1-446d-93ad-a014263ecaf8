import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction, createAuditLogInTransaction, checkRelationsBeforeDelete } from '@/lib/transaction-utils';

export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    // فحص معامل view لتحديد النوع المطلوب
    const { searchParams } = new URL(request.url);
    const view = searchParams.get('view');

    // استرجاع الأجهزة مع ترتيب مختلف حسب النوع
    const orderBy = view === 'simple' 
      ? { id: 'asc' as const }
      : { id: 'desc' as const };

    const devices = await prisma.device.findMany({
      orderBy
    });
    return NextResponse.json(devices);
  } catch (error) {
    console.error('Failed to fetch devices:', error);
    return NextResponse.json({ error: 'Failed to fetch devices' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const newDevice = await request.json();

    // Basic validation
    if (!newDevice.id || !newDevice.model) {
      return NextResponse.json(
        { message: 'Device ID and model are required' },
        { status: 400 }
    );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if device already exists
      const existingDevice = await tx.device.findUnique({
        where: { id: newDevice.id }
      });

      if (existingDevice) {
        throw new Error('Device with this ID already exists');
      }

      // Create new device
      const device = await tx.device.create({
        data: {
          id: newDevice.id,
          model: newDevice.model,
          status: newDevice.status || 'متاح للبيع',
          storage: newDevice.storage || 'N/A',
          price: newDevice.price || 0,
          condition: newDevice.condition || 'جديد',
          warehouseId: newDevice.warehouseId,
          supplierId: newDevice.supplierId,
          dateAdded: new Date().toISOString(),
          replacementInfo: newDevice.replacementInfo
        }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'CREATE',
        details: `Created device: ${device.id} - ${device.model}`,
        tableName: 'device',
        recordId: device.id
      });

      return device;
    });

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error('Failed to create device:', error);

    if (error instanceof Error && error.message === 'Device with this ID already exists') {
      return NextResponse.json(
        { message: 'Device with this ID already exists' },
        { status: 409 }
    );
    }

    return NextResponse.json({ error: 'Failed to create device' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const updatedDevice = await request.json();

    if (!updatedDevice.id) {
      return NextResponse.json(
        { message: 'Device ID is required' },
        { status: 400 }
    );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if device exists
      const existingDevice = await tx.device.findUnique({
        where: { id: updatedDevice.id }
      });

      if (!existingDevice) {
        throw new Error('Device not found');
      }

      // Update device
      const device = await tx.device.update({
        where: { id: updatedDevice.id },
        data: {
          model: updatedDevice.model || existingDevice.model,
          status: updatedDevice.status || existingDevice.status,
          storage: updatedDevice.storage || existingDevice.storage,
          price: updatedDevice.price !== undefined ? updatedDevice.price : existingDevice.price,
          condition: updatedDevice.condition || existingDevice.condition,
          warehouseId: updatedDevice.warehouseId,
          supplierId: updatedDevice.supplierId,
          replacementInfo: updatedDevice.replacementInfo
        }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'UPDATE',
        details: `Updated device: ${device.id} - ${device.model}`,
        tableName: 'device',
        recordId: device.id
      });

      return device;
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to update device:', error);

    if (error instanceof Error && error.message === 'Device not found') {
      return NextResponse.json({ message: 'Device not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to update device' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // التحقق من التفويض - يتطلب صلاحيات إدارية للحذف
    const authResult = await requireAuth(request, 'manager');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { id } = await request.json();

    if (!id) {
      return NextResponse.json(
        { message: 'Device ID is required' },
        { status: 400 }
    );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if device exists
      const existingDevice = await tx.device.findUnique({
        where: { id }
      });

      if (!existingDevice) {
        throw new Error('Device not found');
      }

      // فحص العلاقات قبل الحذف
      const relationCheck = await checkRelationsBeforeDelete(tx, 'device', id);

      if (relationCheck.hasRelations) {
        throw new Error(`Cannot delete device: ${relationCheck.relations.join(', ')}`);
      }

      // Delete device
      await tx.device.delete({
        where: { id }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'DELETE',
        details: `Deleted device: ${existingDevice.id} - ${existingDevice.model}`,
        tableName: 'device',
        recordId: id
      });

      return { message: 'Device deleted successfully' };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to delete device:', error);

    if (error instanceof Error) {
      if (error.message === 'Device not found') {
        return NextResponse.json({ message: 'Device not found' }, { status: 404 });
      }
      if (error.message.startsWith('Cannot delete device:')) {
        const relations = error.message.replace('Cannot delete device: ', '');
        return NextResponse.json({
          error: 'Cannot delete device',
          reason: 'يوجد عمليات مرتبطة بهذا الجهاز',
          relations: relations.split(', ')
        }, { status: 409 });
      }
    }

    return NextResponse.json({ error: 'Failed to delete device' }, { status: 500 });
  }
}
