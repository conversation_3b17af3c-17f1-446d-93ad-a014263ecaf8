const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// دالة لتنظيف البيانات من null bytes (محسنة)
function sanitizeString(str) {
  if (!str) return str;
  if (typeof str !== 'string') return str;
  
  // إزالة null bytes وأحرف التحكم الأخرى
  let cleaned = str
    .replace(/\x00/g, '') // إزالة null bytes
    .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // إزالة أحرف التحكم
    .trim();
    
  return cleaned.length > 0 ? cleaned : null;
}

// دالة لتنظيف بيانات المرتجع
function sanitizeReturnData(data) {
  const sanitized = {};
  
  // نسخ جميع الحقول مع التنظيف
  for (const [key, value] of Object.entries(data)) {
    if (typeof value === 'string') {
      sanitized[key] = sanitizeString(value);
    } else {
      sanitized[key] = value;
    }
  }
  
  return sanitized;
}

async function testDirectReturnCreation() {
  try {
    console.log('اختبار إنشاء مرتجع مباشر مع قاعدة البيانات...');

    // الحصول على جهاز للاختبار
    const device = await prisma.device.findFirst({
      where: { status: 'متاح للبيع' }
    });

    if (!device) {
      console.log('لا توجد أجهزة متاحة للاختبار');
      return;
    }

    console.log(`الجهاز المختار: ${device.id} (${device.model})`);

    // إنشاء رقم مرتجع
    const roNumber = `RO-${Date.now()}`;

    // بيانات المرتجع مع تنظيف محتمل للـ null bytes
    const rawData = {
      roNumber,
      opReturnNumber: roNumber,
      date: new Date(),
      saleId: 1, // مطلوب في schema
      soNumber: 'SO-TEST', // مطلوب في schema
      clientName: 'عميل تجريبي \x00 مع null byte', // إضافة null byte للاختبار
      warehouseName: 'مخزن تجريبي', // مطلوب في schema
      notes: 'مرتجع تجريبي \x00\x01\x02 مع أحرف تحكم',
      status: 'معلق',
      processedBy: null,
      processedDate: null,
      employeeName: 'مختبر النظام \x00 نظيف',
      attachments: null
    };

    console.log('البيانات الخام قبل التنظيف:');
    for (const [key, value] of Object.entries(rawData)) {
      if (typeof value === 'string') {
        console.log(`${key}: "${value}" (contains null byte: ${value.includes('\x00')})`);
      }
    }

    const sanitizedData = sanitizeReturnData(rawData);

    console.log('\nالبيانات بعد التنظيف:');
    for (const [key, value] of Object.entries(sanitizedData)) {
      if (typeof value === 'string') {
        console.log(`${key}: "${value}" (contains null byte: ${value.includes('\x00')})`);
      }
    }

    // إنشاء المرتجع باستخدام raw query
    const returnRecord = await prisma.$queryRaw`
      INSERT INTO "Return" 
      ("roNumber", "opReturnNumber", "date", "saleId", "soNumber", "clientName", "warehouseName", "notes", "status", "processedBy", "processedDate", "employeeName", "attachments")
      VALUES 
      (${sanitizedData.roNumber}, ${sanitizedData.opReturnNumber}, ${sanitizedData.date}, ${sanitizedData.saleId}, ${sanitizedData.soNumber}, ${sanitizedData.clientName}, ${sanitizedData.warehouseName}, ${sanitizedData.notes}, ${sanitizedData.status}, ${sanitizedData.processedBy}, ${sanitizedData.processedDate}, ${sanitizedData.employeeName}, ${sanitizedData.attachments})
      RETURNING *
    `.then(result => Array.isArray(result) ? result[0] : result);

    console.log('✅ تم إنشاء المرتجع بنجاح!');
    console.log('ID:', returnRecord.id);
    console.log('رقم المرتجع:', returnRecord.roNumber);

    // إنشاء عنصر مرتجع
    const sanitizedItemData = {
      returnId: returnRecord.id,
      deviceId: sanitizeString(device.id) || '',
      model: sanitizeString(device.model || '') || '',
      returnReason: sanitizeString('عيب في الجهاز \x00 مع null byte') || '',
      replacementDeviceId: null,
      isReplacement: false,
      originalDeviceId: null
    };

    console.log('بيانات عنصر المرتجع المنظفة:', sanitizedItemData);

    await prisma.$executeRaw`
      INSERT INTO "return_items" 
      ("returnId", "deviceId", "model", "returnReason", "replacementDeviceId", "isReplacement", "originalDeviceId")
      VALUES 
      (${sanitizedItemData.returnId}, ${sanitizedItemData.deviceId}, ${sanitizedItemData.model}, ${sanitizedItemData.returnReason}, ${sanitizedItemData.replacementDeviceId}, ${sanitizedItemData.isReplacement}, ${sanitizedItemData.originalDeviceId})
    `;

    console.log('✅ تم إنشاء عنصر المرتجع بنجاح!');

    // حذف المرتجع التجريبي
    await prisma.returnItem.deleteMany({
      where: { returnId: returnRecord.id }
    });

    await prisma.return.delete({
      where: { id: returnRecord.id }
    });

    console.log('✅ تم حذف المرتجع التجريبي');

    console.log('\n🎉 تم إنجاز جميع العمليات بنجاح! لا توجد مشاكل في null bytes');

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error);
    
    if (error.message.includes('invalid byte sequence')) {
      console.error('المشكلة: null bytes ما زالت موجودة في البيانات');
    } else if (error.message.includes('violates')) {
      console.error('المشكلة: انتهاك قيود قاعدة البيانات');
    }
  } finally {
    await prisma.$disconnect();
  }
}

testDirectReturnCreation();
