"use client";

import React, { useState, useMemo } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ChevronDown, ChevronUp, Eye, Download, Calendar, User, Package, MapPin, Wrench, ShoppingCart } from 'lucide-react';

interface OperationDetail {
  id: string;
  type: string;
  operation: string;
  date: string;
  user: string;
  department: string;
  details: any;
  status: string;
  location?: string;
  reference?: string;
  notes?: string;
}

interface DeviceOperationDetailsProps {
  operation: OperationDetail;
  isExpanded?: boolean;
  onToggleExpand?: () => void;
}

const getOperationIcon = (type: string) => {
  switch (type) {
    case 'supply':
      return <Package className="w-5 h-5 text-cyan-600" />;
    case 'evaluation':
      return <Eye className="w-5 h-5 text-indigo-600" />;
    case 'maintenance':
      return <Wrench className="w-5 h-5 text-amber-600" />;
    case 'sale':
      return <ShoppingCart className="w-5 h-5 text-green-600" />;
    case 'return':
      return <Download className="w-5 h-5 text-red-600" />;
    case 'transfer':
      return <MapPin className="w-5 h-5 text-purple-600" />;
    default:
      return <Package className="w-5 h-5 text-gray-600" />;
  }
};

const getOperationColor = (type: string) => {
  switch (type) {
    case 'supply':
      return 'bg-cyan-50 border-cyan-200';
    case 'evaluation':
      return 'bg-indigo-50 border-indigo-200';
    case 'maintenance':
      return 'bg-amber-50 border-amber-200';
    case 'sale':
      return 'bg-green-50 border-green-200';
    case 'return':
      return 'bg-red-50 border-red-200';
    case 'transfer':
      return 'bg-purple-50 border-purple-200';
    default:
      return 'bg-gray-50 border-gray-200';
  }
};

const getStatusColor = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'completed':
    case 'مكتمل':
    case 'منجز':
      return 'bg-green-100 text-green-800';
    case 'pending':
    case 'في الانتظار':
    case 'معلق':
      return 'bg-yellow-100 text-yellow-800';
    case 'cancelled':
    case 'ملغي':
      return 'bg-red-100 text-red-800';
    case 'in_progress':
    case 'قيد التنفيذ':
      return 'bg-blue-100 text-blue-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const formatOperationTitle = (type: string, operation: string) => {
  const titles = {
    supply: 'توريد جهاز',
    evaluation: 'تقييم جهاز',
    maintenance: 'صيانة جهاز',
    sale: 'بيع جهاز',
    return: 'إرجاع جهاز',
    transfer: 'نقل جهاز'
  };
  
  return titles[type] || operation;
};

const DeviceOperationDetails: React.FC<DeviceOperationDetailsProps> = ({
  operation,
  isExpanded = false,
  onToggleExpand
}) => {
  const [showDetails, setShowDetails] = useState(isExpanded);

  const operationIcon = getOperationIcon(operation.type);
  const operationColor = getOperationColor(operation.type);
  const statusColor = getStatusColor(operation.status);
  const operationTitle = formatOperationTitle(operation.type, operation.operation);

  const detailItems = useMemo(() => {
    const items = [];
    
    if (operation.details) {
      Object.entries(operation.details).forEach(([key, value]) => {
        if (value && key !== 'id') {
          let label = key;
          let displayValue = value;
          
          // تحويل المفاتيح إلى عربي
          const keyMap = {
            quantity: 'الكمية',
            price: 'السعر',
            supplier: 'المورد',
            model: 'الموديل',
            serial: 'الرقم التسلسلي',
            warranty: 'الضمان',
            condition: 'الحالة',
            reason: 'السبب',
            cost: 'التكلفة',
            technician: 'الفني',
            description: 'الوصف',
            customer: 'العميل',
            location: 'الموقع',
            department: 'القسم'
          };
          
          label = keyMap[key] || label;
          
          // تنسيق القيم
          if (typeof value === 'number' && key.includes('price' || 'cost')) {
            displayValue = `${value.toLocaleString()} ج.م`;
          } else if (typeof value === 'boolean') {
            displayValue = value ? 'نعم' : 'لا';
          } else if (Array.isArray(value)) {
            displayValue = value.join(', ');
          }
          
          items.push({ label, value: displayValue });
        }
      });
    }
    
    return items;
  }, [operation.details]);

  const toggleDetails = () => {
    setShowDetails(!showDetails);
    if (onToggleExpand) {
      onToggleExpand();
    }
  };

  return (
    <Card className={`operation-card transition-all duration-300 ${operationColor} ${showDetails ? 'shadow-lg' : 'shadow-md'}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3 space-x-reverse">
            <div className="operation-icon flex-shrink-0">
              {operationIcon}
            </div>
            <div className="flex-1 min-w-0">
              <CardTitle className="operation-title text-lg font-bold text-gray-900">
                {operationTitle}
              </CardTitle>
              <div className="flex items-center space-x-4 space-x-reverse mt-2">
                <div className="flex items-center text-sm text-gray-600">
                  <Calendar className="w-4 h-4 ml-1" />
                  {new Date(operation.date).toLocaleDateString('ar-SA')}
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <User className="w-4 h-4 ml-1" />
                  {operation.user}
                </div>
                {operation.department && (
                  <div className="flex items-center text-sm text-gray-600">
                    <MapPin className="w-4 h-4 ml-1" />
                    {operation.department}
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2 space-x-reverse">
            <Badge className={`status-badge ${statusColor}`}>
              {operation.status}
            </Badge>
            {detailItems.length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleDetails}
                className="p-1 h-auto"
              >
                {showDetails ? (
                  <ChevronUp className="w-4 h-4" />
                ) : (
                  <ChevronDown className="w-4 h-4" />
                )}
              </Button>
            )}
          </div>
        </div>
        
        {operation.reference && (
          <div className="text-sm text-gray-600 mt-2">
            <span className="font-medium">رقم المرجع:</span> {operation.reference}
          </div>
        )}
      </CardHeader>

      {showDetails && detailItems.length > 0 && (
        <CardContent className="pt-0">
          <div className="border-t border-gray-200 pt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {detailItems.map((item, index) => (
                <div key={index} className="flex flex-col">
                  <span className="text-sm font-medium text-gray-700 mb-1">
                    {item.label}
                  </span>
                  <span className="text-sm text-gray-900 bg-white px-3 py-2 rounded-md border">
                    {item.value}
                  </span>
                </div>
              ))}
            </div>
            
            {operation.notes && (
              <div className="mt-4 p-3 bg-white rounded-md border">
                <span className="text-sm font-medium text-gray-700 block mb-2">
                  ملاحظات:
                </span>
                <p className="text-sm text-gray-900">{operation.notes}</p>
              </div>
            )}
            
            {operation.location && (
              <div className="mt-4 p-3 bg-white rounded-md border">
                <span className="text-sm font-medium text-gray-700 block mb-2">
                  الموقع:
                </span>
                <p className="text-sm text-gray-900">{operation.location}</p>
              </div>
            )}
          </div>
        </CardContent>
      )}
    </Card>
  );
};

export default DeviceOperationDetails;
