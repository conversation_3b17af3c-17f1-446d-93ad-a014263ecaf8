/**
 * سكريپت لمنح المستخدم System Administrator جميع الصلاحيات
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// جميع الأقسام المتاحة في النظام
const allSections = [
  'dashboard',
  'clients', 
  'warehouses',
  'inventory',
  'supply',
  'sales',
  'requests',
  'returns',
  'maintenance',
  'maintenanceTransfer',
  'warehouseTransfer',
  'grading',
  'track',
  'stocktaking',
  'acceptDevices',
  'messaging',
  'reports',
  'users',
  'settings',
  'pricing'
];

// إنشاء صلاحيات كاملة لجميع الأقسام
const createFullPermissions = () => {
  const permissions = {};
  
  allSections.forEach(section => {
    permissions[section] = {
      view: true,
      create: true,
      edit: true,
      delete: true,
      viewAll: true,
      manage: [1, 2, 3],
      acceptWithoutWarranty: true
    };
  });

  return permissions;
};

async function grantSystemAdminPermissions() {
  try {
    console.log('🔍 البحث عن المستخدم System Administrator...');
    
    // البحث عن المستخدم بالاسم أو اسم المستخدم
    let user = await prisma.user.findFirst({
      where: {
        OR: [
          { name: { contains: 'System Administrator' } },
          { username: 'admin' },
          { email: '<EMAIL>' }
        ]
      }
    });

    // إذا لم يوجد المستخدم، أنشئه
    if (!user) {
      console.log('👤 إنشاء المستخدم System Administrator...');
      user = await prisma.user.create({
        data: {
          name: 'System Administrator',
          username: 'admin',
          email: '<EMAIL>',
          role: 'admin',
          status: 'Active',
          permissions: JSON.stringify(createFullPermissions())
        }
      });
      console.log('✅ تم إنشاء المستخدم بنجاح:', user.name);
    } else {
      console.log('✅ تم العثور على المستخدم:', user.name);
    }

    // تحديث صلاحيات المستخدم
    console.log('🔧 تحديث الصلاحيات...');
    const fullPermissions = createFullPermissions();
    
    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: {
        permissions: JSON.stringify(fullPermissions),
        role: 'admin',
        status: 'Active'
      }
    });

    console.log('✅ تم منح جميع الصلاحيات للمستخدم System Administrator بنجاح!');
    console.log('📋 معلومات المستخدم:');
    console.log(`   الاسم: ${updatedUser.name}`);
    console.log(`   اسم المستخدم: ${updatedUser.username}`);
    console.log(`   البريد الإلكتروني: ${updatedUser.email}`);
    console.log(`   الدور: ${updatedUser.role}`);
    console.log(`   الحالة: ${updatedUser.status}`);
    console.log(`   عدد الأقسام المتاحة: ${allSections.length}`);
    
    // عرض الصلاحيات الممنوحة
    console.log('\n📊 الصلاحيات الممنوحة:');
    allSections.forEach(section => {
      console.log(`   ✅ ${section}: جميع الصلاحيات (عرض، إنشاء، تعديل، حذف)`);
    });

    // إنشاء audit log
    try {
      await prisma.auditLog.create({
        data: {
          userId: user.id.toString(),
          username: user.username,
          operation: 'UPDATE',
          details: `Granted full permissions to System Administrator`,
          tableName: 'user',
          recordId: user.id.toString(),
          createdAt: new Date()
        }
      });
      console.log('📝 تم تسجيل العملية في سجل التدقيق');
    } catch (auditError) {
      console.warn('⚠️  تعذر إنشاء سجل التدقيق:', auditError.message);
    }

  } catch (error) {
    console.error('❌ خطأ في منح الصلاحيات:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// تشغيل السكريپت
if (require.main === module) {
  grantSystemAdminPermissions()
    .then(() => {
      console.log('\n🎉 تم تنفيذ السكريپت بنجاح!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 فشل في تنفيذ السكريپت:', error);
      process.exit(1);
    });
}

module.exports = { grantSystemAdminPermissions, createFullPermissions };
