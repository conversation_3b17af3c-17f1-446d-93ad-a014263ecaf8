#!/usr/bin/env node

/**
 * سكريبت لمحاكاة دالة checkMaintenanceOrderRelations بالضبط
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function simulateCheckMaintenanceOrderRelations() {
  console.log('🔍 محاكاة دالة checkMaintenanceOrderRelations...\n');

  try {
    // جلب البيانات من قاعدة البيانات (مثل Store)
    const maintenanceOrders = await prisma.maintenanceOrder.findMany();
    const evaluationOrders = await prisma.evaluationOrder.findMany();
    const devices = await prisma.device.findMany();
    
    // محاكاة الدالة لكل أمر صيانة
    for (const orderToDelete of maintenanceOrders) {
      console.log(`\n🔧 فحص إمكانية حذف أمر: ${orderToDelete.orderNumber}`);
      console.log(`   📅 تاريخ الأمر: ${orderToDelete.date}`);
      
      // استخراج معرفات الأجهزة (مثل Store بالضبط)
      let deviceIdsInOrder = [];
      try {
        const items = JSON.parse(orderToDelete.items || '[]');
        deviceIdsInOrder = items.map((item) => item.deviceId || item.id).filter(Boolean);
      } catch (error) {
        console.log('   ⚠️  خطأ في استخراج الأجهزة');
        continue;
      }
      
      console.log(`   📱 معرفات الأجهزة: [${deviceIdsInOrder.join(', ')}]`);
      
      if (deviceIdsInOrder.length === 0) {
        console.log('   ✅ لا توجد أجهزة - يمكن الحذف');
        continue;
      }
      
      const relatedOperations = [];
      const orderDate = new Date(orderToDelete.date);
      console.log(`   📅 تاريخ الأمر المحول: ${orderDate.toISOString()}`);

      // محاكاة فحص أوامر التقييم بالضبط مثل Store
      const relatedEvaluationOrders = evaluationOrders.filter((order) => {
        try {
          const evalDate = new Date(order.date);
          console.log(`     🔍 فحص أمر تقييم: ${order.orderNumber || order.id}`);
          console.log(`       📅 تاريخ التقييم: ${evalDate.toISOString()}`);
          console.log(`       ⏰ مقارنة: ${evalDate.toISOString()} > ${orderDate.toISOString()} = ${evalDate > orderDate}`);
          
          // التأكد من صحة التاريخ
          if (isNaN(evalDate.getTime()) || isNaN(orderDate.getTime())) {
            console.log('       ❌ تاريخ غير صحيح');
            return false;
          }
          
          // فحص الأجهزة المشتركة
          const hasSharedDevices = order.items && Array.isArray(order.items) && 
            order.items.some((item) => {
              const itemDeviceId = item.deviceId || item.id;
              const isShared = deviceIdsInOrder.includes(itemDeviceId);
              if (isShared) {
                console.log(`       📱 جهاز مشترك: ${itemDeviceId}`);
              }
              return isShared;
            });
          
          console.log(`       🔗 أجهزة مشتركة: ${hasSharedDevices ? 'نعم' : 'لا'}`);
          
          // النتيجة النهائية
          const result = hasSharedDevices && evalDate > orderDate;
          console.log(`       🎯 النتيجة: ${result ? 'يمنع الحذف' : 'لا يمنع'}`);
          
          return result;
        } catch (error) {
          console.log(`       ❌ خطأ في المقارنة: ${error.message}`);
          return false;
        }
      });
      
      if (relatedEvaluationOrders.length > 0) {
        const evalOrderNumbers = relatedEvaluationOrders.map(o => o.orderNumber || o.id).join(', ');
        relatedOperations.push(`${relatedEvaluationOrders.length} أمر تقييم لاحق: ${evalOrderNumbers}`);
        console.log(`   ❌ لا يمكن الحذف: ${relatedOperations.join(', ')}`);
      } else {
        console.log('   ✅ يمكن الحذف - لا توجد أوامر تقييم لاحقة مرتبطة');
      }
    }

    console.log('\n✅ انتهت المحاكاة');

  } catch (error) {
    console.error('❌ خطأ في المحاكاة:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// تشغيل المحاكاة
if (require.main === module) {
  simulateCheckMaintenanceOrderRelations()
    .then(() => {
      console.log('\n🎉 تم إكمال المحاكاة');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 فشلت المحاكاة:', error);
      process.exit(1);
    });
}

module.exports = { simulateCheckMaintenanceOrderRelations };
