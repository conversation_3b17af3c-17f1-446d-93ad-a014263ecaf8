import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    // التحقق من المفتاح السري للحماية (اختياري)
    const authHeader = request.headers.get('authorization');
    const expectedToken = process.env.CRON_SECRET || 'default-secret';
    
    if (authHeader !== `Bearer ${expectedToken}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // تشغيل فحص الطلبات المتأخرة
    const result = await NotificationService.checkOverdueRequests();
    
    if (result) {
      return NextResponse.json({ 
        success: true, 
        message: 'Overdue requests check completed successfully' 
      });
    } else {
      return NextResponse.json({ 
        success: false, 
        message: 'Failed to check overdue requests' 
      }, { status: 500 });
    }
  } catch (error) {
    console.error('Error in overdue requests check:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}

// يمكن استدعاؤها أيضاً عبر GET للاختبار
export async function GET(request: NextRequest) {
  try {
    const result = await NotificationService.checkOverdueRequests();
    
    return NextResponse.json({ 
      success: result,
      message: result ? 'Check completed' : 'Check failed',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error in overdue requests check:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}
