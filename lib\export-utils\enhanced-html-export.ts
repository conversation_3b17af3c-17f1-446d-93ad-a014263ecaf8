"use client";

// حل محسن لتصدير التقارير بدون Canvas ومع دعم كامل للعربية
// هذا الحل يستخدم HTML + CSS محسن بدلاً من Canvas
import { formatDate, formatTime, formatDateTime } from '@/lib/date-utils';

interface ReportSettings {
  companyName?: {
    text: string;
    fontSize: 'small' | 'medium' | 'large' | 'custom';
    customFontSize?: string;
    color: string;
    position: 'below-logo' | 'right-logo' | 'left-logo' | 'above-logo';
  };
  companyDetails?: {
    enabled: boolean;
    text: string;
    fontSize: 'small' | 'medium' | 'large' | 'custom';
    customFontSize?: string;
    color: string;
  };
  address?: {
    enabled: boolean;
    text: string;
    fontSize: 'small' | 'medium' | 'large' | 'custom';
    customFontSize?: string;
    color: string;
  };
  logo?: {
    url: string;
    alt?: string;
    maxHeight?: string;
  };
  logoSize?: 'small' | 'medium' | 'large' | 'custom';
  customLogoSize?: {
    width: string;
    height: string;
  };
  qrCode?: {
    enabled: boolean;
    type: 'whatsapp' | 'website' | 'custom';
    value: string;
    size: 'small' | 'medium' | 'large';
    position: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  };
  headerStyle?: 'minimal' | 'standard' | 'detailed';
  footerText?: string;
  showTimestamp?: boolean;
  colorScheme?: 'blue' | 'green' | 'purple' | 'red' | 'orange';
  fontSize?: 'small' | 'medium' | 'large';
  orientation?: 'portrait' | 'landscape';
  language?: 'ar' | 'en' | 'both';
  watermark?: {
    enabled: boolean;
    text: string;
  };
}

interface DeviceTrackingData {
  model: string;
  id: string;
  status: string;
  lastSale?: {
    clientName: string;
    soNumber: string;
    opNumber: string;
    date: string;
  };
  warrantyInfo?: {
    status: string;
    expiryDate: string;
    remaining: string;
  };
}

interface TimelineEvent {
  id: string;
  type: string;
  title: string;
  description: string;
  date: string;
  user?: string;
  formattedDate?: string;
  details?: any;
}

interface SystemSettings {
  logoUrl?: string;
  companyNameAr?: string;
  companyNameEn?: string;
  addressAr?: string;
  addressEn?: string;
  phone?: string;
  email?: string;
  website?: string;
  footerTextAr?: string;
  footerTextEn?: string;
}

/**
 * تصدير تقرير تتبع الجهاز باستخدام HTML محسن بدون Canvas
 */
export async function exportDeviceTrackingReportHTML(
  deviceData: DeviceTrackingData,
  timelineEvents: TimelineEvent[],
  options: {
    fileName?: string;
    isCustomerView?: boolean;
    action?: 'print' | 'download';
    language?: 'ar' | 'en' | 'both';
    systemSettings?: any;
    reportSettings?: any;
  } = {}
): Promise<void> {
  const {
    fileName = 'device_report',
    isCustomerView = false,
    action = 'print',
    language = 'both',
    systemSettings,
    reportSettings
  } = options;

  try {
    // جلب الإعدادات من النظام أو استخدام المرسلة
    const settings = systemSettings || await fetchSystemSettings();
    
    // إعدادات التقارير الافتراضية
    const defaultReportSettings = {
      showLogo: true,
      logoPosition: 'center',
      headerStyle: 'corporate',
      footerStyle: 'detailed',
      colorScheme: 'default',
      fontSize: 'medium',
      pageOrientation: 'portrait',
      language: language,
      includeTimestamp: true,
      includeWatermark: false,
      watermarkText: '',
      customColors: {
        primary: '#4299e1',
        secondary: '#2b6cb0',
        accent: '#6366f1'
      }
    };

    const reportConfig = reportSettings || defaultReportSettings;
    
    // إنشاء محتوى HTML محسن مع الإعدادات الجديدة
    const htmlContent = generateEnhancedHTML(
      deviceData,
      timelineEvents,
      settings,
      isCustomerView,
      language,
      reportConfig,
      reportSettings
    );
    
    if (action === 'print') {
      // طباعة مباشرة
      openPrintWindow(htmlContent, fileName);
    } else {
      // تصدير كـ PDF عبر طباعة إلى PDF
      openPrintToPDFWindow(htmlContent, fileName);
    }
    
    showNotification('تم تحضير التقرير بنجاح!', 'success');
    
  } catch (error) {
    console.error('Error exporting device tracking report:', error);
    showNotification('حدث خطأ أثناء التصدير: ' + (error as Error).message, 'error');
  }
}

/**
 * جلب إعدادات النظام
 */
async function fetchSystemSettings(): Promise<SystemSettings> {
  try {
    const response = await fetch('/api/settings');
    if (response.ok) {
      return await response.json();
    }
  } catch (error) {
    console.warn('فشل في جلب الإعدادات، سيتم استخدام القيم الافتراضية');
  }
  
  // القيم الافتراضية
  return {
    companyNameAr: 'DeviceFlow',
    companyNameEn: 'DeviceFlow',
    addressAr: 'الشارع الرئيسي، المدينة، الدولة',
    addressEn: 'Main Street, City, Country',
    phone: '+************',
    email: '<EMAIL>',
    website: 'www.deviceflow.com',
    footerTextAr: 'شكرًا لتعاملكم معنا.',
    footerTextEn: 'Thank you for your business.'
  };
}

/**
 * إنشاء محتوى HTML محسن مع دعم كامل للعربية وإعدادات التقارير المتقدمة
 */
function generateEnhancedHTML(
  deviceData: DeviceTrackingData,
  timelineEvents: TimelineEvent[],
  settings: SystemSettings,
  isCustomerView: boolean,
  language: string,
  reportConfig?: any,
  reportSettings?: ReportSettings
): string {
  
  // إعدادات التقارير الافتراضية إذا لم يتم تمريرها
  const config = reportConfig || {
    showLogo: true,
    logoPosition: 'center',
    headerStyle: 'corporate',
    footerStyle: 'detailed',
    colorScheme: 'default',
    fontSize: 'medium',
    pageOrientation: 'portrait',
    language: language,
    includeTimestamp: true,
    includeWatermark: false,
    watermarkText: '',
    customColors: {
      primary: '#4299e1',
      secondary: '#2b6cb0',
      accent: '#6366f1'
    }
  };

  // تحديد نظام الألوان
  const colorSchemes = {
    default: { primary: '#4299e1', secondary: '#2b6cb0', accent: '#6366f1' },
    blue: { primary: '#3b82f6', secondary: '#1d4ed8', accent: '#60a5fa' },
    green: { primary: '#10b981', secondary: '#047857', accent: '#34d399' },
    purple: { primary: '#8b5cf6', secondary: '#7c3aed', accent: '#a78bfa' },
    custom: config.customColors
  };

  const colors = colorSchemes[config.colorScheme as keyof typeof colorSchemes] || colorSchemes.default;
  
  // تحديد عنوان التقرير
  const reportTitle = isCustomerView
    ? (language === 'en' ? 'Device Tracking Report (Customer Copy)' : 'تقرير تتبع الجهاز (نسخة العميل)')
    : (language === 'en' ? 'Device History Log' : 'سجل تاريخ الجهاز');
  
  const currentDate = new Date();
  const arabicDate = formatDate(currentDate, { arabic: true });
  const englishDate = formatDate(currentDate);
  const time = formatTime(currentDate);

  return `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>${reportTitle}</title>
    
    <!-- تحميل الخطوط العربية المحسنة -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    
    <style>
        ${getEnhancedCSS(language, config, colors)}
    </style>
</head>

<body class="arabic-optimized">
    ${reportSettings?.watermark?.enabled && reportSettings?.watermark?.text ? 
      `<div class="watermark">${reportSettings.watermark.text}</div>` : 
      config.includeWatermark && config.watermarkText ? 
      `<div class="watermark">${config.watermarkText}</div>` : ''
    }
    
    <!-- رأس التقرير -->
    ${reportSettings ? 
      generateAdvancedHeader(settings, reportSettings, reportTitle) : 
      generateReportHeader(settings, reportTitle, deviceData, language, arabicDate, englishDate, time)
    }
    
    <!-- محتوى التقرير -->
    <div class="report-content">
        <!-- معلومات الجهاز -->
        ${generateDeviceInfoSection(deviceData, language)}
        
        <!-- معلومات البيع (للعملاء فقط) -->
        ${isCustomerView && deviceData.lastSale ? generateSaleInfoSection(deviceData.lastSale, language) : ''}
        
        <!-- معلومات الضمان (للعملاء فقط) -->
        ${isCustomerView && deviceData.warrantyInfo ? generateWarrantyInfoSection(deviceData.warrantyInfo, language) : ''}
        
        <!-- سجل الأحداث -->
        ${timelineEvents.length > 0 ? generateTimelineSection(timelineEvents, isCustomerView, language) : ''}
    </div>
    
    <!-- تذييل التقرير -->
    ${generateReportFooter(settings, language, arabicDate, englishDate, time, reportSettings)}
    
    <script>
        ${getPrintScript()}
    </script>
</body>
</html>`;
}

/**
 * CSS محسن لدعم العربية بدون Canvas مع ألوان ديناميكية
 */
function getEnhancedCSS(language: string, config?: any, colors?: any): string {
  const defaultColors = { primary: '#4299e1', secondary: '#2b6cb0', accent: '#6366f1' };
  const currentColors = colors || defaultColors;
  const fontSize = config?.fontSize || 'medium';
  const pageOrientation = config?.pageOrientation || 'portrait';
  
  const fontSizes: Record<string, any> = {
    small: { base: '12px', h1: '20px', h2: '18px', h3: '16px' },
    medium: { base: '14px', h1: '24px', h2: '20px', h3: '18px' },
    large: { base: '16px', h1: '28px', h2: '24px', h3: '20px' }
  };
  
  const currentFontSizes = fontSizes[fontSize] || fontSizes.medium;
  
  return `
    /* متغيرات CSS للألوان الديناميكية */
    :root {
      --primary-color: ${currentColors.primary};
      --secondary-color: ${currentColors.secondary};
      --accent-color: ${currentColors.accent};
      --font-size-base: ${currentFontSizes.base};
      --font-size-h1: ${currentFontSizes.h1};
      --font-size-h2: ${currentFontSizes.h2};
      --font-size-h3: ${currentFontSizes.h3};
    }
    
    /* إعدادات أساسية محسنة للعربية */
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        -webkit-font-feature-settings: "liga", "kern";
        font-feature-settings: "liga", "kern";
    }
    
    html {
        direction: rtl;
        lang: ar;
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
    
    body {
        /* خطوط عربية محسنة مرتبة بالأولوية */
        font-family: 'Cairo', 'Noto Sans Arabic', 'Tajawal', 'Segoe UI Historic', 'Apple Color Emoji', Arial, sans-serif !important;
        direction: rtl;
        text-align: right;
        background: #ffffff;
        color: #1a202c;
        line-height: 1.7;
        font-size: var(--font-size-base);
        
        /* إعدادات طباعة محسنة */
        print-color-adjust: exact;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
        
        /* تحسين عرض النص العربي */
        text-rendering: optimizeLegibility;
        font-variant-ligatures: normal;
        font-variant-numeric: normal;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }
    
    /* تحسين عرض النصوص العربية */
    .arabic-optimized {
        unicode-bidi: embed;
        direction: rtl;
        writing-mode: horizontal-tb;
    }
    
    .arabic-text {
        font-family: 'Cairo', 'Noto Sans Arabic', 'Tajawal', Arial, sans-serif !important;
        direction: rtl;
        text-align: right;
        unicode-bidi: bidi-override;
        word-break: break-word;
        hyphens: auto;
    }
    
    /* تنسيق العناوين */
    h1, h2, h3, h4, h5, h6 {
        font-family: 'Cairo', 'Noto Sans Arabic', 'Tajawal', Arial, sans-serif !important;
        font-weight: 700;
        color: var(--secondary-color);
        margin-bottom: 16px;
        line-height: 1.4;
        letter-spacing: 0.02em;
    }
    
    h1 { font-size: var(--font-size-h1); }
    h2 { font-size: var(--font-size-h2); }
    h3 { font-size: var(--font-size-h3); }
    h4 { font-size: var(--font-size-base); }
    
    /* رأس التقرير */
    .report-header {
        text-align: center;
        padding: 30px 20px;
        border-bottom: 3px solid var(--primary-color);
        margin-bottom: 30px;
        background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    }
    
    .company-info {
        margin-bottom: 20px;
    }
    
    .company-name-ar {
        font-size: var(--font-size-h1);
        font-weight: 700;
        color: var(--secondary-color);
        margin-bottom: 8px;
    }
    
    .company-name-en {
        font-size: 18px;
        color: #4a5568;
        margin-bottom: 12px;
    }
    
    .company-address {
        font-size: 14px;
        color: #718096;
        margin-bottom: 6px;
    }
    
    .contact-info {
        font-size: 13px;
        color: #718096;
    }
    
    .report-title {
        font-size: 24px;
        font-weight: 700;
        color: #2d3748;
        margin: 20px 0 10px 0;
    }
    
    .report-subtitle {
        font-size: 16px;
        color: #4a5568;
        margin-bottom: 10px;
    }
    
    .print-timestamp {
        font-size: 12px;
        color: #a0aec0;
    }
    
    /* محتوى التقرير */
    .report-content {
        padding: 0 20px;
        max-width: 800px;
        margin: 0 auto;
    }
    
    /* أقسام المعلومات */
    .info-section {
        margin: 25px 0;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        background: #ffffff;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    
    .section-header {
        background: #f7fafc;
        padding: 16px 20px;
        border-bottom: 1px solid #e2e8f0;
    }
    
    .section-title {
        font-size: 18px;
        font-weight: 600;
        color: #2d3748;
        margin: 0;
    }
    
    .section-content {
        padding: 20px;
    }
    
    /* شبكة المعلومات */
    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 16px;
    }
    
    .info-item {
        display: flex;
        align-items: flex-start;
        gap: 8px;
        padding: 12px;
        background: #f7fafc;
        border-radius: 6px;
        border-right: 4px solid #4299e1;
    }
    
    .info-label {
        font-weight: 600;
        color: #4a5568;
        min-width: 100px;
        white-space: nowrap;
    }
    
    .info-value {
        color: #2d3748;
        word-break: break-word;
        flex: 1;
    }
    
    /* سجل الأحداث */
    .timeline-container {
        margin: 25px 0;
    }
    
    .timeline-event {
        display: flex;
        gap: 16px;
        margin-bottom: 20px;
        padding: 16px;
        background: #ffffff;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        position: relative;
    }
    
    .timeline-event::before {
        content: '';
        position: absolute;
        right: -1px;
        top: 0;
        bottom: 0;
        width: 4px;
        background: #4299e1;
        border-radius: 0 4px 4px 0;
    }
    
    .event-icon {
        flex-shrink: 0;
        width: 12px;
        height: 12px;
        background: #4299e1;
        border-radius: 50%;
        margin-top: 6px;
    }
    
    .event-content {
        flex: 1;
    }
    
    .event-title {
        font-weight: 600;
        color: #2d3748;
        font-size: 16px;
        margin-bottom: 6px;
    }
    
    .event-description {
        color: #4a5568;
        margin-bottom: 8px;
        line-height: 1.6;
    }
    
    .event-meta {
        font-size: 12px;
        color: #718096;
        display: flex;
        gap: 16px;
        flex-wrap: wrap;
    }
    
    /* تذييل التقرير */
    .report-footer {
        margin-top: 40px;
        padding: 20px;
        border-top: 2px solid #e2e8f0;
        text-align: center;
        background: #f7fafc;
    }
    
    .footer-text {
        font-size: 14px;
        color: #4a5568;
        margin-bottom: 8px;
    }
    
    .footer-timestamp {
        font-size: 12px;
        color: #a0aec0;
    }

    /* العلامة المائية */
    .watermark {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) rotate(-45deg);
        font-size: 60px;
        color: rgba(0, 0, 0, 0.05);
        z-index: -1;
        pointer-events: none;
        font-weight: bold;
        white-space: nowrap;
        user-select: none;
    }
    
    /* إعدادات الطباعة المحسنة */
    @media print {
        @page {
            size: A4;
            margin: 15mm;
            direction: rtl;
        }
        
        body {
            font-size: 12pt !important;
            line-height: 1.5 !important;
            background: white !important;
            color: black !important;
        }
        
        .report-header {
            background: white !important;
            border-bottom: 2px solid #000 !important;
        }
        
        .info-section {
            page-break-inside: avoid;
            border: 1px solid #000 !important;
            box-shadow: none !important;
        }
        
        .timeline-event {
            page-break-inside: avoid;
            border: 1px solid #ccc !important;
            box-shadow: none !important;
        }
        
        .timeline-event::before {
            background: #000 !important;
        }
        
        .section-header {
            background: #f0f0f0 !important;
            border-bottom: 1px solid #000 !important;
        }
        
        .info-item {
            background: #f8f8f8 !important;
            border-right: 2px solid #000 !important;
        }
        
        .no-print {
            display: none !important;
        }
        
        /* تحسين الخطوط للطباعة */
        h1, h2, h3, h4, h5, h6 {
            color: #000 !important;
        }
        
        .company-name-ar {
            color: #000 !important;
        }
        
        .report-title {
            color: #000 !important;
        }
    }
    
    /* تحسينات للشاشات الصغيرة */
    @media (max-width: 768px) {
        .info-grid {
            grid-template-columns: 1fr;
        }
        
        .report-content {
            padding: 0 15px;
        }
        
        .report-header {
            padding: 20px 15px;
        }
    }
    
    /* تحسينات إضافية للنص العربي */
    .arabic-number {
        font-family: 'Cairo', 'Noto Sans Arabic', monospace !important;
        direction: ltr;
        display: inline-block;
    }
    
    .bilingual-text {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }
    
    .text-ar {
        font-family: 'Cairo', 'Noto Sans Arabic', Arial, sans-serif !important;
        direction: rtl;
        text-align: right;
    }
    
    .text-en {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        direction: ltr;
        text-align: left;
        color: #666;
        font-size: 0.9em;
    }
  `;
}

/**
 * إنشاء QR Code كـ SVG
 */
function generateQRCode(value: string, size: number = 100): string {
  // استخدام مولد QR بسيط (يمكن استبداله بمكتبة QR حقيقية)
  const cellSize = size / 25; // 25x25 grid
  const randomPattern = value.split('').map(char => char.charCodeAt(0)).join('');
  
  let svgContent = `<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">`;
  svgContent += `<rect width="${size}" height="${size}" fill="white"/>`;
  
  // إنشاء نمط QR مبسط
  for (let i = 0; i < 25; i++) {
    for (let j = 0; j < 25; j++) {
      const shouldFill = (i + j + parseInt(randomPattern.charAt((i * j) % randomPattern.length))) % 3 === 0;
      if (shouldFill) {
        svgContent += `<rect x="${j * cellSize}" y="${i * cellSize}" width="${cellSize}" height="${cellSize}" fill="black"/>`;
      }
    }
  }
  
  svgContent += '</svg>';
  return `data:image/svg+xml;base64,${btoa(svgContent)}`;
}

/**
 * إنشاء محتوى رأس الصفحة المحسن
 */
function generateAdvancedHeader(
  settings: SystemSettings,
  reportSettings?: ReportSettings,
  reportTitle?: string
): string {
  const logoSize = reportSettings?.logoSize || 'medium';
  const logoSizeMap = {
    small: '40px',
    medium: '60px',
    large: '80px',
    custom: reportSettings?.customLogoSize ? 
      `width: ${reportSettings.customLogoSize.width}px; height: ${reportSettings.customLogoSize.height}px;` : '60px'
  };

  const companyNameFontSize = reportSettings?.companyName?.fontSize || 'large';
  const companyNameFontSizeMap = {
    small: '18px',
    medium: '24px',
    large: '32px',
    custom: reportSettings?.companyName?.customFontSize ? `${reportSettings.companyName.customFontSize}px` : '32px'
  };

  const companyDetailsFontSize = reportSettings?.companyDetails?.fontSize || 'medium';
  const companyDetailsFontSizeMap = {
    small: '12px',
    medium: '16px',
    large: '20px',
    custom: reportSettings?.companyDetails?.customFontSize ? `${reportSettings.companyDetails.customFontSize}px` : '16px'
  };

  const addressFontSize = reportSettings?.address?.fontSize || 'small';
  const addressFontSizeMap = {
    small: '11px',
    medium: '14px',
    large: '18px',
    custom: reportSettings?.address?.customFontSize ? `${reportSettings.address.customFontSize}px` : '11px'
  };

  // تحديد موقع اسم الشركة
  const companyNamePosition = reportSettings?.companyName?.position || 'below-logo';
  const isHorizontalLayout = companyNamePosition === 'right-logo' || companyNamePosition === 'left-logo';

  return `
    <div class="report-header" style="display: flex; align-items: ${isHorizontalLayout ? 'center' : 'flex-start'}; justify-content: space-between; margin-bottom: 30px; position: relative;">
      
      <!-- QR Code إذا كان مفعل -->
      ${reportSettings?.qrCode?.enabled && reportSettings?.qrCode?.value ? `
        <div class="qr-code" style="
          position: absolute;
          ${reportSettings.qrCode.position === 'top-right' ? 'top: 0; right: 0;' : ''}
          ${reportSettings.qrCode.position === 'top-left' ? 'top: 0; left: 0;' : ''}
          ${reportSettings.qrCode.position === 'bottom-right' ? 'bottom: 0; right: 0;' : ''}
          ${reportSettings.qrCode.position === 'bottom-left' ? 'bottom: 0; left: 0;' : ''}
          z-index: 10;
        ">
          <img src="${generateQRCode(
            reportSettings.qrCode.type === 'whatsapp' ? `https://wa.me/${reportSettings.qrCode.value.replace(/[^\d]/g, '')}` :
            reportSettings.qrCode.type === 'website' ? reportSettings.qrCode.value :
            reportSettings.qrCode.value
          )}" 
          alt="QR Code" 
          style="width: ${reportSettings.qrCode.size === 'small' ? '50px' : reportSettings.qrCode.size === 'large' ? '120px' : '80px'};" />
        </div>
      ` : ''}

      <!-- محتوى الرأس الرئيسي -->
      <div class="header-main" style="display: flex; ${isHorizontalLayout ? 'flex-direction: row; align-items: center;' : 'flex-direction: column; align-items: center;'} gap: 20px; flex: 1;">
        
        ${companyNamePosition === 'above-logo' && reportSettings?.companyName?.text ? `
          <div class="company-name" style="
            font-size: ${companyNameFontSizeMap[companyNameFontSize]};
            color: ${reportSettings.companyName.color};
            font-weight: bold;
            text-align: center;
          ">
            ${reportSettings.companyName.text}
          </div>
        ` : ''}

        <div style="display: flex; ${companyNamePosition === 'right-logo' ? 'flex-direction: row-reverse;' : companyNamePosition === 'left-logo' ? 'flex-direction: row;' : 'flex-direction: column;'} align-items: center; gap: 15px;">
          
          <!-- الشعار -->
          ${settings.logoUrl ? `
            <div class="logo-container">
              <img src="${settings.logoUrl}" alt="شعار الشركة" style="
                ${logoSize === 'custom' ? logoSizeMap.custom : `height: ${logoSizeMap[logoSize]};`}
                object-fit: contain;
              " />
            </div>
          ` : ''}

          <!-- اسم الشركة (حسب الموقع المحدد) -->
          ${(companyNamePosition === 'below-logo' || companyNamePosition === 'right-logo' || companyNamePosition === 'left-logo') && reportSettings?.companyName?.text ? `
            <div class="company-info" style="text-align: ${isHorizontalLayout ? 'right' : 'center'};">
              <div class="company-name" style="
                font-size: ${companyNameFontSizeMap[companyNameFontSize]};
                color: ${reportSettings.companyName.color};
                font-weight: bold;
                margin-bottom: 8px;
              ">
                ${reportSettings.companyName.text}
              </div>
              
              <!-- تفاصيل الشركة -->
              ${reportSettings?.companyDetails?.enabled && reportSettings?.companyDetails?.text ? `
                <div class="company-details" style="
                  font-size: ${companyDetailsFontSizeMap[companyDetailsFontSize]};
                  color: ${reportSettings.companyDetails.color};
                  margin-bottom: 6px;
                ">
                  ${reportSettings.companyDetails.text}
                </div>
              ` : ''}
              
              <!-- عنوان الشركة -->
              ${reportSettings?.address?.enabled && reportSettings?.address?.text ? `
                <div class="company-address" style="
                  font-size: ${addressFontSizeMap[addressFontSize]};
                  color: ${reportSettings.address.color};
                ">
                  ${reportSettings.address.text}
                </div>
              ` : ''}
            </div>
          ` : ''}
        </div>

      </div>

      <!-- عنوان التقرير -->
      ${reportTitle ? `
        <div class="report-title" style="
          text-align: center;
          font-size: 20px;
          font-weight: bold;
          color: var(--primary-color);
          margin-top: 20px;
          border-bottom: 2px solid var(--primary-color);
          padding-bottom: 10px;
        ">
          ${reportTitle}
        </div>
      ` : ''}
    </div>
  `;
}

/**
 * إنشاء رأس التقرير
 */
function generateReportHeader(
  settings: SystemSettings,
  title: string,
  deviceData: DeviceTrackingData,
  language: string,
  arabicDate: string,
  englishDate: string,
  time: string
): string {
  return `
    <div class="report-header">
        <div class="company-info">
            ${language === 'ar' || language === 'both' ? `
                <div class="company-name-ar">${settings.companyNameAr || 'DeviceFlow'}</div>
            ` : ''}
            ${language === 'en' || language === 'both' ? `
                <div class="company-name-en">${settings.companyNameEn || 'DeviceFlow'}</div>
            ` : ''}
            ${language === 'ar' || language === 'both' ? `
                <div class="company-address">${settings.addressAr || 'الشارع الرئيسي، المدينة، الدولة'}</div>
            ` : ''}
            ${language === 'en' || language === 'both' ? `
                <div class="company-address">${settings.addressEn || 'Main Street, City, Country'}</div>
            ` : ''}
            <div class="contact-info">
                ${settings.phone ? `📞 ${settings.phone}` : ''}
                ${settings.email ? ` | ✉️ ${settings.email}` : ''}
                ${settings.website ? ` | 🌐 ${settings.website}` : ''}
            </div>
        </div>
        
        <div class="report-info">
            <h1 class="report-title">${title}</h1>
            <div class="report-subtitle">${deviceData.model} - ${deviceData.id}</div>
            <div class="print-timestamp">
                ${language === 'ar' || language === 'both' ? `تاريخ الطباعة: ${arabicDate} - ${time}` : ''}
                ${language === 'both' ? ' | ' : ''}
                ${language === 'en' || language === 'both' ? `Print Date: ${englishDate} - ${time}` : ''}
            </div>
        </div>
    </div>
  `;
}

/**
 * إنشاء قسم معلومات الجهاز
 */
function generateDeviceInfoSection(deviceData: DeviceTrackingData, language: string): string {
  return `
    <div class="info-section">
        <div class="section-header">
            <h3 class="section-title">
                ${language === 'en' ? 'Device Information' : 'معلومات الجهاز'}
            </h3>
        </div>
        <div class="section-content">
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">
                        ${language === 'en' ? 'Model:' : 'الموديل:'}
                    </span>
                    <span class="info-value arabic-text">${deviceData.model || '-'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">
                        ${language === 'en' ? 'Serial Number:' : 'الرقم التسلسلي:'}
                    </span>
                    <span class="info-value arabic-number">${deviceData.id || '-'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">
                        ${language === 'en' ? 'Status:' : 'الحالة:'}
                    </span>
                    <span class="info-value arabic-text">${deviceData.status || '-'}</span>
                </div>
            </div>
        </div>
    </div>
  `;
}

/**
 * إنشاء قسم معلومات البيع
 */
function generateSaleInfoSection(lastSale: NonNullable<DeviceTrackingData['lastSale']>, language: string): string {
  return `
    <div class="info-section">
        <div class="section-header">
            <h3 class="section-title">
                ${language === 'en' ? 'Sale Information' : 'تفاصيل البيع'}
            </h3>
        </div>
        <div class="section-content">
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">
                        ${language === 'en' ? 'Customer:' : 'العميل:'}
                    </span>
                    <span class="info-value arabic-text">${lastSale.clientName || '-'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">
                        ${language === 'en' ? 'Sales Invoice:' : 'فاتورة البيع:'}
                    </span>
                    <span class="info-value arabic-number">${lastSale.soNumber || '-'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">
                        ${language === 'en' ? 'Official Invoice:' : 'الفاتورة الرسمية:'}
                    </span>
                    <span class="info-value arabic-number">${lastSale.opNumber || 'لا يوجد'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">
                        ${language === 'en' ? 'Sale Date:' : 'تاريخ البيع:'}
                    </span>
                    <span class="info-value">${formatDate(lastSale.date, { arabic: true })}</span>
                </div>
            </div>
        </div>
    </div>
  `;
}

/**
 * إنشاء قسم معلومات الضمان
 */
function generateWarrantyInfoSection(warrantyInfo: NonNullable<DeviceTrackingData['warrantyInfo']>, language: string): string {
  return `
    <div class="info-section">
        <div class="section-header">
            <h3 class="section-title">
                ${language === 'en' ? 'Warranty Information' : 'حالة الضمان'}
            </h3>
        </div>
        <div class="section-content">
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">
                        ${language === 'en' ? 'Status:' : 'الحالة:'}
                    </span>
                    <span class="info-value arabic-text">${warrantyInfo.status || '-'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">
                        ${language === 'en' ? 'Expiry Date:' : 'تاريخ الانتهاء:'}
                    </span>
                    <span class="info-value">${warrantyInfo.expiryDate || '-'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">
                        ${language === 'en' ? 'Remaining Time:' : 'الوقت المتبقي:'}
                    </span>
                    <span class="info-value arabic-text">${warrantyInfo.remaining || '-'}</span>
                </div>
            </div>
        </div>
    </div>
  `;
}

/**
 * إنشاء قسم سجل الأحداث
 */
function generateTimelineSection(timelineEvents: TimelineEvent[], isCustomerView: boolean, language: string): string {
  // تصفية الأحداث للعرض للعملاء
  const filteredEvents = isCustomerView 
    ? timelineEvents.filter(event => !event.type.includes('توريد') && !event.type.includes('تقييم'))
    : timelineEvents;

  const eventsHTML = filteredEvents.map(event => `
    <div class="timeline-event">
        <div class="event-icon"></div>
        <div class="event-content">
            <div class="event-title arabic-text">${event.title}</div>
            <div class="event-description arabic-text">${event.description}</div>
            <div class="event-meta">
                <span>${event.formattedDate || formatDate(event.date, { arabic: true })}</span>
                ${event.user ? `<span>بواسطة: ${event.user}</span>` : ''}
            </div>
        </div>
    </div>
  `).join('');

  return `
    <div class="info-section">
        <div class="section-header">
            <h3 class="section-title">
                ${language === 'en' ? 'Event Timeline' : 'سجل الأحداث'}
            </h3>
        </div>
        <div class="section-content">
            <div class="timeline-container">
                ${eventsHTML}
            </div>
        </div>
    </div>
  `;
}

/**
 * إنشاء تذييل التقرير
 */
function generateReportFooter(
  settings: SystemSettings,
  language: string,
  arabicDate: string,
  englishDate: string,
  time: string,
  reportSettings?: ReportSettings
): string {
  const footerText = reportSettings?.footerText || 
    (language === 'ar' || language === 'both' ? settings.footerTextAr || 'شكرًا لتعاملكم معنا.' : '') ||
    (language === 'en' || language === 'both' ? settings.footerTextEn || 'Thank you for your business.' : '');
    
  const showTimestamp = reportSettings?.showTimestamp !== false; // افتراضياً true
  
  return `
    <div class="report-footer">
        ${footerText ? `<div class="footer-text">${footerText}</div>` : ''}
        ${showTimestamp ? `
          <div class="footer-timestamp">
            ${language === 'ar' || language === 'both' ? `تم الإنشاء: ${arabicDate} - ${time}` : ''}
            ${language === 'both' ? ' | ' : ''}
            ${language === 'en' || language === 'both' ? `Generated: ${englishDate} - ${time}` : ''}
          </div>
        ` : ''}
    </div>
  `;
}

/**
 * سكريبت الطباعة المحسن
 */
function getPrintScript(): string {
  return `
    // انتظار تحميل جميع الخطوط والموارد
    document.fonts.ready.then(() => {
      // انتظار إضافي للتأكد من تحميل كل شيء
      setTimeout(() => {
        // إعداد الطباعة
        window.print();
        
        // إغلاق النافذة بعد الطباعة
        window.onafterprint = () => {
          window.close();
        };
        
        // إغلاق تلقائي في حالة عدم الطباعة
        setTimeout(() => {
          if (!window.closed) {
            window.close();
          }
        }, 60000); // إغلاق بعد دقيقة
        
      }, 2000); // انتظار ثانيتين لضمان التحميل الكامل
    });
  `;
}

/**
 * فتح نافذة طباعة
 */
function openPrintWindow(htmlContent: string, fileName: string): void {
  const printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes');
  
  if (!printWindow) {
    throw new Error('فشل في فتح نافذة الطباعة. تأكد من عدم حظر النوافذ المنبثقة.');
  }
  
  printWindow.document.write(htmlContent);
  printWindow.document.close();
}

/**
 * فتح نافذة طباعة إلى PDF
 */
function openPrintToPDFWindow(htmlContent: string, fileName: string): void {
  const printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes');
  
  if (!printWindow) {
    throw new Error('فشل في فتح نافذة الطباعة. تأكد من عدم حظر النوافذ المنبثقة.');
  }
  
  // إضافة تعليمات للمستخدم لحفظ كـ PDF
  const modifiedHTML = htmlContent.replace(
    '<body class="arabic-optimized">',
    `<body class="arabic-optimized">
      <div class="no-print" style="position: fixed; top: 10px; left: 10px; background: #4299e1; color: white; padding: 10px; border-radius: 5px; z-index: 1000; font-family: Arial; font-size: 12px;">
        اضغط Ctrl+P واختر "حفظ كـ PDF" من خيارات الطباعة
      </div>`
  );
  
  printWindow.document.write(modifiedHTML);
  printWindow.document.close();
}

/**
 * عرض إشعار للمستخدم
 */
function showNotification(message: string, type: 'success' | 'error' = 'success'): void {
  const notification = document.createElement("div");
  notification.textContent = message;
  notification.style.position = "fixed";
  notification.style.top = "20px";
  notification.style.right = "20px";
  notification.style.padding = "12px 20px";
  notification.style.background = type === 'success' ? "#10B981" : "#EF4444";
  notification.style.color = "white";
  notification.style.borderRadius = "8px";
  notification.style.zIndex = "9999";
  notification.style.boxShadow = "0 4px 12px rgba(0, 0, 0, 0.15)";
  notification.style.fontSize = "14px";
  notification.style.fontWeight = "500";
  notification.style.fontFamily = "Cairo, Arial, sans-serif";
  notification.style.direction = "rtl";
  notification.setAttribute('data-notification-id', 'enhanced-html-export');
  
  document.body.appendChild(notification);
  
  setTimeout(() => {
    if (document.body.contains(notification)) {
      document.body.removeChild(notification);
    }
  }, 5000);
}

// تصدير الدالة الرئيسية
export default exportDeviceTrackingReportHTML;
