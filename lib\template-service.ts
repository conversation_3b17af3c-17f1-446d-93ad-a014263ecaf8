// خدمة القوالب - دوال مساعدة فقط (بدون prisma)
import { formatDate, formatTime } from '@/lib/date-utils';

export interface TemplateVariable {
  key: string;
  label: string;
  description: string;
  example: string;
}

export interface ResponseTemplate {
  id: number;
  name: string;
  category: 'approval' | 'rejection' | 'clarification' | 'custom';
  title: string;
  content: string;
  variables?: TemplateVariable[];
  isSystem: boolean;
  isActive: boolean;
  usageCount: number;
  createdBy?: number;
  createdAt: string;
  updatedAt: string;
}

// المتغيرات المتاحة في القوالب
export const TEMPLATE_VARIABLES: TemplateVariable[] = [
  {
    key: 'employeeName',
    label: 'اسم الموظف',
    description: 'اسم الموظف صاحب الطلب',
    example: 'أحمد محمد'
  },
  {
    key: 'requestNumber',
    label: 'رقم الطلب',
    description: 'رقم الطلب الفريد',
    example: 'REQ-0001'
  },
  {
    key: 'requestType',
    label: 'نوع الطلب',
    description: 'نوع الطلب (تعديل، حذف، إعادة نظر)',
    example: 'تعديل'
  },
  {
    key: 'priority',
    label: 'أولوية الطلب',
    description: 'أولوية الطلب (عادي، طاريء، طاريء جدا)',
    example: 'طاريء'
  },
  {
    key: 'currentDate',
    label: 'التاريخ الحالي',
    description: 'تاريخ اليوم',
    example: '2024-01-15'
  },
  {
    key: 'adminName',
    label: 'اسم المدير',
    description: 'اسم المدير الذي يرد على الطلب',
    example: 'سارة أحمد'
  }
];

export class TemplateService {
  // دوال مساعدة فقط - جميع عمليات قاعدة البيانات في API routes



  // استبدال المتغيرات في القالب
  static replaceVariables(
    content: string,
    variables: Record<string, string>
  ): string {
    let result = content;
    
    // استبدال المتغيرات المخصصة
    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
      result = result.replace(regex, value);
    });

    // استبدال المتغيرات الثابتة
    const now = new Date();
    result = result.replace(/\{\{currentDate\}\}/g, formatDate(now, { arabic: true }));
    result = result.replace(/\{\{currentTime\}\}/g, formatTime(now));

    return result;
  }


}
