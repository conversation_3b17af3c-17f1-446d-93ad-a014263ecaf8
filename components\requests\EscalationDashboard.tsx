'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';
import {
  AlertTriangle,
  Clock,
  TrendingUp,
  Users,
  Bell,
  CheckCircle,
  XCircle,
  Timer,
  Zap
} from 'lucide-react';
import { format } from 'date-fns';

interface EscalationStats {
  totalPending: number;
  overdue: number;
  urgent: number;
  critical: number;
  averageResponseTime: number;
  escalationRate: number;
}

interface OverdueRequest {
  id: number;
  requestNumber: string;
  employeeName: string;
  requestType: string;
  priority: string;
  requestDate: string;
  minutesOverdue: number;
  escalationLevel: number;
  nextEscalationIn: number;
}

export default function EscalationDashboard() {
  const [stats, setStats] = useState<EscalationStats>({
    totalPending: 0,
    overdue: 0,
    urgent: 0,
    critical: 0,
    averageResponseTime: 0,
    escalationRate: 0
  });
  const [overdueRequests, setOverdueRequests] = useState<OverdueRequest[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  // جلب إحصائيات التصعيد
  const fetchEscalationStats = async () => {
    try {
      const response = await fetch('/api/escalation/stats');
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('خطأ في جلب إحصائيات التصعيد:', error);
    }
  };

  // جلب الطلبات المتأخرة
  const fetchOverdueRequests = async () => {
    try {
      const response = await fetch('/api/escalation/overdue');
      if (response.ok) {
        const data = await response.json();
        setOverdueRequests(data);
      }
    } catch (error) {
      console.error('خطأ في جلب الطلبات المتأخرة:', error);
    }
  };

  // تشغيل فحص التصعيد يدوياً
  const runEscalationCheck = async () => {
    try {
      const response = await fetch('/api/notifications/check-overdue', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.NEXT_PUBLIC_CRON_SECRET || 'default-secret'}`
        }
      });

      if (response.ok) {
        toast({
          title: 'تم تشغيل فحص التصعيد',
          description: 'تم فحص الطلبات المتأخرة وإرسال الإشعارات المناسبة'
        });
        
        // إعادة تحميل البيانات
        await fetchData();
      } else {
        throw new Error('فشل في تشغيل فحص التصعيد');
      }
    } catch (error) {
      console.error('خطأ في تشغيل فحص التصعيد:', error);
      toast({
        title: 'خطأ',
        description: 'فشل في تشغيل فحص التصعيد',
        variant: 'destructive'
      });
    }
  };

  // جلب جميع البيانات
  const fetchData = async () => {
    setIsLoading(true);
    await Promise.all([
      fetchEscalationStats(),
      fetchOverdueRequests()
    ]);
    setIsLoading(false);
  };

  useEffect(() => {
    fetchData();
    
    // تحديث البيانات كل دقيقة
    const interval = setInterval(fetchData, 60000);
    return () => clearInterval(interval);
  }, []);

  // حساب نسبة التقدم للتصعيد
  const getEscalationProgress = (minutesOverdue: number, priority: string) => {
    const thresholds = {
      'عادي': 24 * 60,
      'طاريء': 4 * 60,
      'طاريء جدا': 60
    };
    
    const threshold = thresholds[priority as keyof typeof thresholds] || thresholds['عادي'];
    return Math.min((minutesOverdue / threshold) * 100, 100);
  };

  // لون التقدم
  const getProgressColor = (progress: number) => {
    if (progress >= 100) return 'bg-red-500';
    if (progress >= 75) return 'bg-orange-500';
    if (progress >= 50) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  // أيقونة الأولوية
  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'طاريء جدا':
        return <Zap className="h-4 w-4 text-red-600" />;
      case 'طاريء':
        return <AlertTriangle className="h-4 w-4 text-orange-600" />;
      default:
        return <Clock className="h-4 w-4 text-blue-600" />;
    }
  };

  // تنسيق الوقت المتبقي
  const formatTimeRemaining = (minutes: number) => {
    if (minutes <= 0) return 'متأخر';
    
    if (minutes >= 60) {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      return `${hours}س ${remainingMinutes}د`;
    }
    
    return `${minutes}د`;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2 space-x-reverse">
              <Clock className="h-8 w-8 text-blue-600" />
              <div>
                <p className="text-2xl font-bold">{stats.totalPending}</p>
                <p className="text-sm text-gray-600">طلبات معلقة</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2 space-x-reverse">
              <AlertTriangle className="h-8 w-8 text-orange-600" />
              <div>
                <p className="text-2xl font-bold">{stats.overdue}</p>
                <p className="text-sm text-gray-600">طلبات متأخرة</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2 space-x-reverse">
              <Zap className="h-8 w-8 text-red-600" />
              <div>
                <p className="text-2xl font-bold">{stats.urgent}</p>
                <p className="text-sm text-gray-600">طلبات طارئة</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2 space-x-reverse">
              <TrendingUp className="h-8 w-8 text-green-600" />
              <div>
                <p className="text-2xl font-bold">{stats.averageResponseTime}س</p>
                <p className="text-sm text-gray-600">متوسط الاستجابة</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* أدوات التحكم */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2 space-x-reverse">
              <Bell className="h-5 w-5 text-primary" />
              <span>إدارة التصعيد</span>
            </CardTitle>
            <Button onClick={runEscalationCheck} variant="outline">
              <Timer className="h-4 w-4 ml-1" />
              فحص التصعيد الآن
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600">معدل التصعيد</p>
              <p className="text-2xl font-bold text-orange-600">{stats.escalationRate}%</p>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600">طلبات حرجة</p>
              <p className="text-2xl font-bold text-red-600">{stats.critical}</p>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600">آخر فحص</p>
              <p className="text-sm font-medium">{format(new Date(), 'HH:mm')}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* الطلبات المتأخرة */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 space-x-reverse">
            <AlertTriangle className="h-5 w-5 text-orange-600" />
            <span>الطلبات المتأخرة ({overdueRequests.length})</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {overdueRequests.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <CheckCircle className="h-12 w-12 mx-auto mb-3 text-green-500" />
              <p>لا توجد طلبات متأخرة</p>
              <p className="text-sm">جميع الطلبات في الوقت المحدد</p>
            </div>
          ) : (
            <div className="space-y-4">
              {overdueRequests.map((request) => {
                const progress = getEscalationProgress(request.minutesOverdue, request.priority);
                return (
                  <div
                    key={request.id}
                    className="p-4 border rounded-lg hover:shadow-sm transition-shadow"
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        {getPriorityIcon(request.priority)}
                        <span className="font-medium">{request.requestNumber}</span>
                        <Badge variant="outline">{request.priority}</Badge>
                        <span className="text-sm text-gray-600">من {request.employeeName}</span>
                      </div>
                      <div className="text-sm text-gray-500">
                        متأخر {formatTimeRemaining(request.minutesOverdue)}
                      </div>
                    </div>
                    
                    <div className="mb-3">
                      <div className="flex items-center justify-between text-sm mb-1">
                        <span>تقدم التصعيد</span>
                        <span>{Math.round(progress)}%</span>
                      </div>
                      <Progress 
                        value={progress} 
                        className={`h-2 ${getProgressColor(progress)}`}
                      />
                    </div>

                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>مستوى التصعيد: {request.escalationLevel}</span>
                      <span>
                        التصعيد التالي في: {formatTimeRemaining(request.nextEscalationIn)}
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
