const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function cleanTestRecord() {
  try {
    await prisma.$executeRaw`DELETE FROM "DeliveryOrder" WHERE "deliveryOrderNumber" = 'TEST-DIRECT'`;
    console.log('تم حذف السجل');
  } catch (error) {
    console.log('خطأ في الحذف:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

cleanTestRecord();
