const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkMaintenanceLogSchema() {
  try {
    console.log('🔍 Checking MaintenanceLog table schema...');
    
    // فحص المخطط
    const result = await prisma.$queryRaw`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'maintenance_logs' 
      ORDER BY ordinal_position;
    `;
    
    console.log('📊 MaintenanceLog schema:');
    result.forEach(col => {
      console.log(`  - ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
    });
    
    // تجربة إنشاء سجل اختبار
    console.log('\n🧪 Testing maintenance log creation...');
    
    const testLog = await prisma.maintenanceLog.create({
      data: {
        deviceId: 'TEST-DEVICE',
        model: 'Test Model',
        repairDate: new Date().toISOString(),
        notes: 'Test maintenance log',
        result: 'Test completed',
        status: 'pending'
      }
    });
    
    console.log('✅ Test log created successfully:', testLog.id);
    
    // حذف السجل التجريبي
    await prisma.maintenanceLog.delete({
      where: { id: testLog.id }
    });
    
    console.log('🧹 Test log cleaned up');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    if (error.code) {
      console.error('💾 Error code:', error.code);
    }
  } finally {
    await prisma.$disconnect();
  }
}

checkMaintenanceLogSchema();
