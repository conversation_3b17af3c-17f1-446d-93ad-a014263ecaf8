// مها<PERSON> Cron للتحقق من الطلبات المتأخرة وإرسال التذكيرات

export class CronJobs {
  private static intervals: NodeJS.Timeout[] = [];

  // بدء جميع مهام Cron
  static startAll() {
    this.startOverdueRequestsCheck();
    console.log('تم بدء جميع مهام Cron');
  }

  // إيقاف جميع مهام Cron
  static stopAll() {
    this.intervals.forEach(interval => clearInterval(interval));
    this.intervals = [];
    console.log('تم إيقاف جميع مهام Cron');
  }

  // فحص الطلبات المتأخرة كل 15 دقيقة
  private static startOverdueRequestsCheck() {
    const interval = setInterval(async () => {
      try {
        console.log('بدء فحص الطلبات المتأخرة...');
        // استدعاء API endpoint للفحص
        const response = await fetch('/api/notifications/check-overdue', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${process.env.CRON_SECRET || 'default-secret'}`
          }
        });

        if (response.ok) {
          console.log('تم فحص الطلبات المتأخرة بنجاح');
        } else {
          console.error('فشل في فحص الطلبات المتأخرة');
        }
      } catch (error) {
        console.error('خطأ في فحص الطلبات المتأخرة:', error);
      }
    }, 15 * 60 * 1000); // كل 15 دقيقة

    this.intervals.push(interval);
  }

  // فحص فوري للطلبات المتأخرة (للاختبار)
  static async checkOverdueNow() {
    try {
      console.log('فحص فوري للطلبات المتأخرة...');
      const response = await fetch('/api/notifications/check-overdue', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.CRON_SECRET || 'default-secret'}`
        }
      });

      const result = response.ok;
      console.log('نتيجة الفحص:', result);
      return result;
    } catch (error) {
      console.error('خطأ في الفحص الفوري:', error);
      return false;
    }
  }
}

// بدء مهام Cron عند تحميل الوحدة (في بيئة الإنتاج)
if (process.env.NODE_ENV === 'production') {
  CronJobs.startAll();
}

// تنظيف عند إغلاق التطبيق
process.on('SIGINT', () => {
  CronJobs.stopAll();
  process.exit(0);
});

process.on('SIGTERM', () => {
  CronJobs.stopAll();
  process.exit(0);
});
