const { Client } = require('pg');

async function resetDatabase() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    user: 'postgres', // استخدم المستخدم الأساسي
    password: 'admin', // كلمة المرور
    database: 'postgres' // الاتصال بقاعدة البيانات الافتراضية
  });

  try {
    await client.connect();
    console.log('🔗 Connected to PostgreSQL');

    // قتل الاتصالات النشطة
    console.log('🔪 Terminating active connections...');
    await client.query(`
      SELECT pg_terminate_backend(pg_stat_activity.pid)
      FROM pg_stat_activity
      WHERE pg_stat_activity.datname = 'deviceflow_db'
        AND pid <> pg_backend_pid()
    `);

    // حذف قاعدة البيانات
    console.log('🗑️ Dropping database...');
    await client.query('DROP DATABASE IF EXISTS deviceflow_db');

    // إنشاء قاعدة البيانات من جديد
    console.log('🏗️ Creating fresh database...');
    await client.query('CREATE DATABASE deviceflow_db');

    console.log('✅ Database reset successful!');

  } catch (error) {
    console.error('❌ Reset failed:', error);
  } finally {
    await client.end();
  }
}

resetDatabase();
