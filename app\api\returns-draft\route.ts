import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';

// GET - استرجاع المسودات المحفوظة للمرتجعات
export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || authResult.user!.id.toString();

    // استرجاع المسودات للمستخدم الحالي
    const drafts = await prisma.$queryRaw`
      SELECT * FROM returns_order_drafts 
      WHERE "userId" = ${parseInt(userId)}
      ORDER BY "updatedAt" DESC
    `;

    return NextResponse.json({
      success: true,
      drafts
    });

  } catch (error) {
    console.error('Failed to fetch returns drafts:', error);
    return NextResponse.json(
      { error: 'Failed to fetch returns drafts' },
      { status: 500 }
    );
  }
}

// POST - حفظ مسودة مرتجعات جديدة أو تحديث موجودة
export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const draftData = await request.json();

    // التحقق من البيانات الأساسية
    if (!draftData.formState && !draftData.returnItems) {
      return NextResponse.json(
        { error: 'Draft data is required' },
        { status: 400 }
      );
    }

    const userId = authResult.user!.id;

    // البحث عن مسودة موجودة للمستخدم
    const existingDrafts = await prisma.$queryRaw`
      SELECT * FROM returns_order_drafts 
      WHERE "userId" = ${userId}
      LIMIT 1
    `;

    let savedDraft;

    if (Array.isArray(existingDrafts) && existingDrafts.length > 0) {
      // تحديث المسودة الموجودة
      const existingDraft = existingDrafts[0] as any;
      savedDraft = await prisma.$queryRaw`
        UPDATE returns_order_drafts 
        SET "formState" = ${JSON.stringify(draftData.formState || {})},
            "returnItems" = ${JSON.stringify(draftData.returnItems || [])},
            attachments = ${JSON.stringify(draftData.attachments || [])},
            "roNumber" = ${draftData.roNumber || null},
            "updatedAt" = CURRENT_TIMESTAMP
        WHERE id = ${existingDraft.id}
        RETURNING *
      `;
    } else {
      // إنشاء مسودة جديدة
      savedDraft = await prisma.$queryRaw`
        INSERT INTO returns_order_drafts ("userId", "formState", "returnItems", attachments, "roNumber")
        VALUES (${userId}, ${JSON.stringify(draftData.formState || {})}, ${JSON.stringify(draftData.returnItems || [])}, 
                ${JSON.stringify(draftData.attachments || [])}, ${draftData.roNumber || null})
        RETURNING *
      `;
    }

    return NextResponse.json({
      success: true,
      draft: savedDraft,
      message: 'تم حفظ مسودة المرتجعات في قاعدة البيانات بنجاح'
    });

  } catch (error) {
    console.error('Failed to save returns draft:', error);
    return NextResponse.json(
      { error: 'Failed to save returns draft' },
      { status: 500 }
    );
  }
}

// DELETE - حذف مسودة مرتجعات
export async function DELETE(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const draftId = searchParams.get('draftId');
    const userId = authResult.user!.id;

    if (draftId) {
      // حذف مسودة محددة
      await prisma.$queryRaw`
        DELETE FROM returns_order_drafts 
        WHERE id = ${parseInt(draftId)} AND "userId" = ${userId}
      `;
    } else {
      // حذف جميع مسودات المستخدم
      await prisma.$queryRaw`
        DELETE FROM returns_order_drafts 
        WHERE "userId" = ${userId}
      `;
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف مسودة المرتجعات بنجاح'
    });

  } catch (error) {
    console.error('Failed to delete returns draft:', error);
    return NextResponse.json(
      { error: 'Failed to delete returns draft' },
      { status: 500 }
    );
  }
}
