const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function fixReturnDateFormat() {
  console.log('🔧 بدء إصلاح تنسيق التواريخ في جدول المرتجعات...');
  try {
    // استعلام خام لتجنب أخطاء Prisma في تحليل التواريخ
    const returns = await prisma.$queryRaw`SELECT id, "roNumber", date::text, "processedDate"::text, "createdAt"::text FROM "Return"`;

    console.log(`📊 تم العثور على ${returns.length} سجل للفحص.`);
    let updatedCount = 0;

    for (const ret of returns) {
      let needsUpdate = false;
      const updates = {};

      // دالة لتحويل التاريخ بأمان
      const convertToISO = (dateString, fieldName) => {
        if (!dateString) return null;
        // تحقق من التنسيق الخاطئ (بدون T و Z)
        if (dateString.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/)) {
          const isoDate = new Date(dateString.replace(' ', 'T') + '.000Z');
          if (!isNaN(isoDate.getTime())) {
            console.log(`   - ${fieldName} للمرتجع ${ret.roNumber}: سيتم تحويل "${dateString}" إلى ${isoDate.toISOString()}`);
            return isoDate;
          }
        }
        return null; // لا تغيير إذا كان التنسيق صحيحاً أو غير متوقع
      };

      const newDate = convertToISO(ret.date, 'date');
      if (newDate) {
        updates.date = newDate;
        needsUpdate = true;
      }

      const newProcessedDate = convertToISO(ret.processedDate, 'processedDate');
      if (newProcessedDate) {
        updates.processedDate = newProcessedDate;
        needsUpdate = true;
      }
      
      const newCreatedAt = convertToISO(ret.createdAt, 'createdAt');
      if (newCreatedAt) {
        updates.createdAt = newCreatedAt;
        needsUpdate = true;
      }

      if (needsUpdate) {
        await prisma.return.update({
          where: { id: ret.id },
          data: updates,
        });
        updatedCount++;
      }
    }

    if (updatedCount > 0) {
      console.log(`\n✅ تم تحديث ${updatedCount} سجل بنجاح!`);
    } else {
      console.log('\n✅ لا توجد سجلات تحتاج إلى تحديث. التواريخ سليمة.');
    }

  } catch (error) {
    console.error('❌ حدث خطأ أثناء إصلاح تنسيق التواريخ:', error);
  } finally {
    await prisma.$disconnect();
    console.log('🔚 انتهت عملية الإصلاح.');
  }
}

fixReturnDateFormat();
