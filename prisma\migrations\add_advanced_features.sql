-- إضافة جدول الإشعارات
CREATE TABLE IF NOT EXISTS "notifications" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER NOT NULL,
    "type" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "requestId" INTEGER,
    "requestNumber" TEXT,
    "employeeName" TEXT,
    "priority" TEXT,
    "actionRequired" BOOLEAN NOT NULL DEFAULT false,
    "read" BOOLEAN NOT NULL DEFAULT false,
    "readAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "notifications_pkey" PRIMARY KEY ("id")
);

-- إضافة جدول تعليقات الطلبات
CREATE TABLE IF NOT EXISTS "request_comments" (
    "id" SERIAL NOT NULL,
    "requestId" INTEGER NOT NULL,
    "userId" INTEGER NOT NULL,
    "userName" TEXT NOT NULL,
    "userRole" TEXT NOT NULL,
    "comment" TEXT NOT NULL,
    "commentType" TEXT NOT NULL DEFAULT 'comment',
    "attachments" JSONB,
    "isInternal" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "request_comments_pkey" PRIMARY KEY ("id")
);

-- إضافة جدول قوالب الردود
CREATE TABLE IF NOT EXISTS "response_templates" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "variables" JSONB,
    "isSystem" BOOLEAN NOT NULL DEFAULT false,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "usageCount" INTEGER NOT NULL DEFAULT 0,
    "createdBy" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "response_templates_pkey" PRIMARY KEY ("id")
);

-- إضافة جدول مرفقات الطلبات
CREATE TABLE IF NOT EXISTS "request_attachments" (
    "id" SERIAL NOT NULL,
    "requestId" INTEGER NOT NULL,
    "fileName" TEXT NOT NULL,
    "fileType" TEXT NOT NULL,
    "fileSize" INTEGER NOT NULL,
    "filePath" TEXT NOT NULL,
    "fileUrl" TEXT,
    "mimeType" TEXT NOT NULL,
    "uploadedBy" INTEGER NOT NULL,
    "uploadedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "isDeleted" BOOLEAN NOT NULL DEFAULT false,
    "deletedAt" TIMESTAMP(3),

    CONSTRAINT "request_attachments_pkey" PRIMARY KEY ("id")
);

-- تحديث جدول طلبات الموظفين
ALTER TABLE "employee_requests" ADD COLUMN IF NOT EXISTS "attachments" JSONB;
ALTER TABLE "employee_requests" ADD COLUMN IF NOT EXISTS "tags" JSONB;
ALTER TABLE "employee_requests" ADD COLUMN IF NOT EXISTS "isArchived" BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE "employee_requests" ADD COLUMN IF NOT EXISTS "archivedAt" TIMESTAMP(3);
ALTER TABLE "employee_requests" ADD COLUMN IF NOT EXISTS "searchVector" TEXT;

-- إضافة العلاقات الخارجية
ALTER TABLE "notifications" ADD CONSTRAINT "notifications_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "request_comments" ADD CONSTRAINT "request_comments_requestId_fkey" FOREIGN KEY ("requestId") REFERENCES "employee_requests"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "response_templates" ADD CONSTRAINT "response_templates_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "request_attachments" ADD CONSTRAINT "request_attachments_requestId_fkey" FOREIGN KEY ("requestId") REFERENCES "employee_requests"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- إضافة فهارس للأداء
CREATE INDEX IF NOT EXISTS "notifications_userId_idx" ON "notifications"("userId");
CREATE INDEX IF NOT EXISTS "notifications_requestId_idx" ON "notifications"("requestId");
CREATE INDEX IF NOT EXISTS "notifications_type_idx" ON "notifications"("type");
CREATE INDEX IF NOT EXISTS "notifications_read_idx" ON "notifications"("read");

CREATE INDEX IF NOT EXISTS "request_comments_requestId_idx" ON "request_comments"("requestId");
CREATE INDEX IF NOT EXISTS "request_comments_userId_idx" ON "request_comments"("userId");

CREATE INDEX IF NOT EXISTS "response_templates_category_idx" ON "response_templates"("category");
CREATE INDEX IF NOT EXISTS "response_templates_isActive_idx" ON "response_templates"("isActive");

CREATE INDEX IF NOT EXISTS "request_attachments_requestId_idx" ON "request_attachments"("requestId");
CREATE INDEX IF NOT EXISTS "request_attachments_isDeleted_idx" ON "request_attachments"("isDeleted");

CREATE INDEX IF NOT EXISTS "employee_requests_status_idx" ON "employee_requests"("status");
CREATE INDEX IF NOT EXISTS "employee_requests_priority_idx" ON "employee_requests"("priority");
CREATE INDEX IF NOT EXISTS "employee_requests_isArchived_idx" ON "employee_requests"("isArchived");
CREATE INDEX IF NOT EXISTS "employee_requests_requestDate_idx" ON "employee_requests"("requestDate");

-- إدراج القوالب الافتراضية
INSERT INTO "response_templates" ("name", "category", "title", "content", "isSystem", "createdAt", "updatedAt") VALUES
('موافقة عامة', 'approval', 'تم الموافقة على طلبك', 'عزيزي {{employeeName}}،

تم الموافقة على طلبك {{requestNumber}} من نوع {{requestType}}.

سيتم تنفيذ الطلب في أقرب وقت ممكن.

شكراً لك.', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

('رفض عام', 'rejection', 'تم رفض طلبك', 'عزيزي {{employeeName}}،

نأسف لإبلاغك بأنه تم رفض طلبك {{requestNumber}}.

يرجى مراجعة الملاحظات المرفقة والتواصل معنا للمزيد من التوضيحات.

شكراً لتفهمك.', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

('طلب توضيح', 'clarification', 'نحتاج توضيحات إضافية', 'عزيزي {{employeeName}}،

بخصوص طلبك {{requestNumber}}، نحتاج إلى بعض التوضيحات الإضافية قبل المتابعة.

يرجى الرد على الاستفسارات في قسم المحادثة.

شكراً لتعاونك.', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)

ON CONFLICT DO NOTHING;
