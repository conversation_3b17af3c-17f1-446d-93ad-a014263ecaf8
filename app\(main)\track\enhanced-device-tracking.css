/* تحسينات CSS لصفحة تتبع الأجهزة المتقدم */

.device-tracking-enhanced {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تحسينات لبطاقات العمليات */
.operation-card {
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
}

.operation-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.operation-card.supply {
  border-left-color: #06b6d4;
  background: linear-gradient(to right, #f0f9ff, #ffffff);
}

.operation-card.evaluation {
  border-left-color: #6366f1;
  background: linear-gradient(to right, #f0f4ff, #ffffff);
}

.operation-card.maintenance {
  border-left-color: #f59e0b;
  background: linear-gradient(to right, #fffbeb, #ffffff);
}

.operation-card.sale {
  border-left-color: #10b981;
  background: linear-gradient(to right, #f0fdf4, #ffffff);
}

.operation-card.return {
  border-left-color: #ef4444;
  background: linear-gradient(to right, #fef2f2, #ffffff);
}

.operation-card.transfer {
  border-left-color: #8b5cf6;
  background: linear-gradient(to right, #f5f3ff, #ffffff);
}

/* تحسينات للأيقونات */
.operation-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 12px;
  margin-left: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.operation-icon:hover {
  transform: scale(1.1);
}

/* تحسينات للنصوص */
.operation-title {
  font-weight: 700;
  font-size: 1.125rem;
  margin-bottom: 4px;
  color: #1f2937;
}

.operation-description {
  line-height: 1.6;
  color: #6b7280;
  margin-bottom: 8px;
}

.operation-meta {
  font-size: 0.875rem;
  color: #9ca3af;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* تحسينات للخط الزمني */
.timeline-connector {
  position: absolute;
  left: 25px;
  top: 60px;
  bottom: -20px;
  width: 2px;
  background: linear-gradient(to bottom, #e5e7eb, #f3f4f6);
}

.timeline-connector.last {
  display: none;
}

/* تحسينات للشارات */
.status-badge {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 6px;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.status-badge.success {
  background-color: #dcfce7;
  color: #166534;
}

.status-badge.warning {
  background-color: #fef3c7;
  color: #92400e;
}

.status-badge.error {
  background-color: #fee2e2;
  color: #991b1b;
}

.status-badge.info {
  background-color: #dbeafe;
  color: #1e40af;
}

/* تحسينات للفلاتر */
.filters-container {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
}

.filter-group {
  margin-bottom: 16px;
}

.filter-label {
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  display: block;
}

.filter-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-chip {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #d1d5db;
  background-color: #ffffff;
  color: #6b7280;
}

.filter-chip:hover {
  background-color: #f3f4f6;
  border-color: #9ca3af;
}

.filter-chip.active {
  background-color: #3b82f6;
  color: #ffffff;
  border-color: #3b82f6;
}

/* تحسينات للإحصائيات */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
}

.stat-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

/* تحسينات للطباعة */
@media print {
  .device-tracking-enhanced {
    font-size: 12px;
  }
  
  .operation-card {
    page-break-inside: avoid;
    margin-bottom: 16px;
    box-shadow: none;
    border: 1px solid #e5e7eb;
  }
  
  .filters-container {
    display: none;
  }
  
  .no-print {
    display: none !important;
  }
}

/* تحسينات للوضع المظلم */
@media (prefers-color-scheme: dark) {
  .operation-card {
    background: #1f2937;
    border-color: #374151;
  }
  
  .operation-title {
    color: #f9fafb;
  }
  
  .operation-description {
    color: #d1d5db;
  }
  
  .operation-meta {
    color: #9ca3af;
  }
  
  .filters-container {
    background: #1f2937;
    border-color: #374151;
  }
  
  .filter-label {
    color: #f9fafb;
  }
  
  .filter-chip {
    background-color: #374151;
    color: #d1d5db;
    border-color: #4b5563;
  }
  
  .filter-chip:hover {
    background-color: #4b5563;
  }
  
  .stat-card {
    background: #1f2937;
    border-color: #374151;
  }
}

/* تحسينات للاستجابة */
@media (max-width: 768px) {
  .operation-card {
    padding: 16px;
  }
  
  .operation-icon {
    width: 40px;
    height: 40px;
    margin-left: 12px;
  }
  
  .operation-title {
    font-size: 1rem;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
  
  .stat-number {
    font-size: 1.5rem;
  }
  
  .filters-container {
    padding: 16px;
  }
}
