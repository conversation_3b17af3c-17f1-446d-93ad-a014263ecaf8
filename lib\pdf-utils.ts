import jsPDF from 'jspdf';
import { SystemSettings } from '@/lib/types';
import { formatDate, formatTime } from '@/lib/date-utils';

export interface PdfHeaderFooterOptions {
  language?: 'ar' | 'en' | 'both';
  showLogo?: boolean;
  showFooter?: boolean;
  showTimestamp?: boolean;
  customTitle?: string;
  customSubtitle?: string;
}

/**
 * دالة محدثة لإنشاء ترويسة وتذييل PDF مع دعم ثنائي اللغة
 */
export const getPdfHeaderFooter = (
  doc: jsPDF, 
  settings: SystemSettings,
  options: PdfHeaderFooterOptions = {}
) => {
  const {
    language = 'both',
    showLogo = true,
    showFooter = true,
    showTimestamp = true,
    customTitle,
    customSubtitle
  } = options;

  const addHeader = () => {
    // إضافة الشعار
    if (showLogo && settings.logoUrl) {
      try {
        doc.addImage(settings.logoUrl, 'PNG', 15, 10, 20, 20);
      } catch (e) {
        console.error('Error adding logo image to PDF:', e);
      }
    }

    let yPosition = 15;

    // أسماء الشركة
    if (language === 'ar' || language === 'both') {
      doc.setFontSize(16);
      doc.setFont('helvetica', 'bold');
      doc.text(settings.companyNameAr, 190, yPosition, { align: 'right' });
      yPosition += 7;
    }

    if (language === 'en' || language === 'both') {
      doc.setFontSize(language === 'both' ? 12 : 16);
      doc.setFont('helvetica', language === 'both' ? 'normal' : 'bold');
      doc.text(settings.companyNameEn, language === 'both' ? 190 : 190, yPosition, { 
        align: language === 'both' ? 'right' : 'right' 
      });
      yPosition += language === 'both' ? 5 : 7;
    }

    // العناوين
    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    
    if (language === 'ar' || language === 'both') {
      doc.text(settings.addressAr, 190, yPosition, { align: 'right' });
      yPosition += 4;
    }

    if (language === 'en' || language === 'both') {
      doc.text(settings.addressEn, 190, yPosition, { align: 'right' });
      yPosition += 4;
    }

    // معلومات الاتصال
    const contactInfo = [];
    if (settings.phone) contactInfo.push(`📞 ${settings.phone}`);
    if (settings.email) contactInfo.push(`✉️ ${settings.email}`);
    if (settings.website) contactInfo.push(`🌐 ${settings.website}`);

    if (contactInfo.length > 0) {
      doc.text(contactInfo.join(' | '), 190, yPosition, { align: 'right' });
      yPosition += 4;
    }

    // العنوان المخصص
    if (customTitle) {
      yPosition += 5;
      doc.setFontSize(18);
      doc.setFont('helvetica', 'bold');
      doc.text(customTitle, 105, yPosition, { align: 'center' });
      yPosition += 7;

      if (customSubtitle) {
        doc.setFontSize(12);
        doc.setFont('helvetica', 'normal');
        doc.text(customSubtitle, 105, yPosition, { align: 'center' });
        yPosition += 5;
      }
    }

    // خط فاصل
    doc.setLineWidth(0.5);
    doc.line(15, yPosition + 2, 195, yPosition + 2);

    return yPosition + 8; // إرجاع الموضع التالي للمحتوى
  };

  const addFooter = () => {
    if (!showFooter) return;

    const pageHeight = doc.internal.pageSize.height;
    let yPosition = pageHeight - 25;

    // خط فاصل
    doc.setLineWidth(0.5);
    doc.line(15, yPosition, 195, yPosition);
    yPosition += 5;

    // نصوص التذييل
    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');

    if (language === 'ar' || language === 'both') {
      if (settings.footerTextAr) {
        doc.text(settings.footerTextAr, 105, yPosition, { align: 'center' });
        yPosition += 4;
      }
    }

    if (language === 'en' || language === 'both') {
      if (settings.footerTextEn) {
        doc.text(settings.footerTextEn, 105, yPosition, { align: 'center' });
        yPosition += 4;
      }
    }

    // الطابع الزمني
    if (showTimestamp) {
      yPosition += 2;
      doc.setFontSize(8);
      doc.setFont('helvetica', 'italic');
      
      const currentDate = new Date();
      const arabicDate = formatDate(currentDate, { arabic: true });
      const englishDate = formatDate(currentDate);
      const time = formatTime(currentDate);

      if (language === 'ar' || language === 'both') {
        const timestampAr = `تاريخ الطباعة: ${arabicDate} - ${time}`;
        doc.text(timestampAr, language === 'both' ? 15 : 105, yPosition, { 
          align: language === 'both' ? 'left' : 'center' 
        });
      }

      if (language === 'en' || language === 'both') {
        const timestampEn = `Print Date: ${englishDate} - ${time}`;
        doc.text(timestampEn, language === 'both' ? 195 : 105, yPosition, { 
          align: language === 'both' ? 'right' : 'center' 
        });
      }
    }

    // رقم الصفحة
    const pageNumber = doc.internal.getCurrentPageInfo().pageNumber;
    const totalPages = doc.internal.getNumberOfPages();
    doc.setFontSize(8);
    doc.text(`${pageNumber} / ${totalPages}`, 105, pageHeight - 5, { align: 'center' });
  };

  return { addHeader, addFooter };
};

/**
 * دالة مساعدة لإنشاء PDF مع الإعدادات الافتراضية
 */
export const createPdfWithSettings = (
  settings: SystemSettings,
  title: string,
  subtitle?: string,
  options: PdfHeaderFooterOptions = {}
): { doc: jsPDF; addHeader: () => number; addFooter: () => void } => {
  const doc = new jsPDF();
  doc.setR2L(true);

  const { addHeader, addFooter } = getPdfHeaderFooter(doc, settings, {
    ...options,
    customTitle: title,
    customSubtitle: subtitle
  });

  return { doc, addHeader, addFooter };
};

/**
 * دالة لحفظ أو طباعة PDF
 */
export const savePdf = (
  doc: jsPDF,
  filename: string,
  action: 'save' | 'print' = 'save'
) => {
  if (action === 'print') {
    doc.output('dataurlnewwindow');
  } else {
    doc.save(filename);
  }
};

/**
 * دالة لإضافة نص عربي محسن
 */
export const addArabicText = (
  doc: jsPDF,
  text: string,
  x: number,
  y: number,
  options: any = {}
) => {
  // معالجة النص العربي إذا لزم الأمر
  const processedText = text; // يمكن إضافة معالجة خاصة هنا
  doc.text(processedText, x, y, options);
};

/**
 * دالة لإضافة جدول مع دعم النصوص العربية
 */
export const addArabicTable = (
  doc: jsPDF,
  headers: string[],
  data: string[][],
  startY: number,
  options: any = {}
) => {
  // استخدام autoTable مع إعدادات عربية
  const autoTable = require('jspdf-autotable');
  
  autoTable(doc, {
    startY,
    head: [headers],
    body: data,
    styles: { 
      font: 'helvetica', 
      halign: 'right',
      fontSize: 10,
      ...options.styles 
    },
    headStyles: { 
      halign: 'center', 
      fillColor: [44, 51, 51],
      textColor: [255, 255, 255],
      fontStyle: 'bold',
      ...options.headStyles 
    },
    theme: 'grid',
    ...options
  });
};
