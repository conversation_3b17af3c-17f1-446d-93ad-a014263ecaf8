const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function analyzeDates() {
  try {
    console.log('📅 تحليل تفصيلي للتواريخ\n');

    // أمر الصيانة
    const maintenanceOrder = await prisma.maintenanceOrder.findFirst({
      where: { orderNumber: 'MTRANS-1' },
      select: { orderNumber: true, date: true }
    });

    // أمر التقييم
    const evaluationOrder = await prisma.evaluationOrder.findFirst({
      where: { orderId: 'EVAL-1' },
      select: { orderId: true, date: true }
    });

    if (maintenanceOrder && evaluationOrder) {
      const maintenanceDate = new Date(maintenanceOrder.date);
      const evalDate = new Date(evaluationOrder.date);

      console.log('🔧 أمر الصيانة MTRANS-1:');
      console.log(`   التاريخ الأصلي: ${maintenanceOrder.date}`);
      console.log(`   كـ Date object: ${maintenanceDate}`);
      console.log(`   ISO: ${maintenanceDate.toISOString()}`);
      console.log(`   Timestamp: ${maintenanceDate.getTime()}`);
      console.log(`   التوقيت المحلي: ${maintenanceDate.toLocaleString('ar-EG', { timeZone: 'Asia/Riyadh' })}`);
      
      console.log('\n📊 أمر التقييم EVAL-1:');
      console.log(`   التاريخ الأصلي: ${evaluationOrder.date}`);
      console.log(`   كـ Date object: ${evalDate}`);
      console.log(`   ISO: ${evalDate.toISOString()}`);
      console.log(`   Timestamp: ${evalDate.getTime()}`);
      console.log(`   التوقيت المحلي: ${evalDate.toLocaleString('ar-EG', { timeZone: 'Asia/Riyadh' })}`);
      
      console.log('\n🔍 المقارنة:');
      console.log(`   الفرق بالميللي ثانية: ${evalDate.getTime() - maintenanceDate.getTime()}`);
      console.log(`   الفرق بالدقائق: ${(evalDate.getTime() - maintenanceDate.getTime()) / 60000}`);
      console.log(`   أمر التقييم ${evalDate > maintenanceDate ? 'بعد' : 'قبل'} أمر الصيانة`);
      
      // معلومات إضافية عن إنشاء الأوامر
      console.log('\n📝 معلومات إضافية:');
      console.log(`   تاريخ إنشاء أمر الصيانة في قاعدة البيانات: ${maintenanceDate.toISOString()}`);
      console.log(`   تاريخ إنشاء أمر التقييم في قاعدة البيانات: ${evalDate.toISOString()}`);
    }

    console.log('\n' + '='.repeat(60));
    console.log('💡 إذا كان هناك خطأ في التواريخ، يمكننا تصحيحها');

  } catch (error) {
    console.error('خطأ:', error);
  } finally {
    await prisma.$disconnect();
  }
}

analyzeDates();
