import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';

// GET - استرجاع المسودات المحفوظة للمبيعات
export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || authResult.user!.id.toString();

    // استرجاع المسودات للمستخدم الحالي
    const drafts = await prisma.$queryRaw`
      SELECT * FROM sales_order_drafts 
      WHERE "userId" = ${parseInt(userId)}
      ORDER BY "updatedAt" DESC
    `;

    return NextResponse.json({
      success: true,
      drafts
    });

  } catch (error) {
    console.error('Failed to fetch sales drafts:', error);
    return NextResponse.json(
      { error: 'Failed to fetch sales drafts' },
      { status: 500 }
    );
  }
}

// POST - حفظ مسودة مبيعات جديدة أو تحديث موجودة
export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const draftData = await request.json();

    // التحقق من البيانات الأساسية
    if (!draftData.formState && !draftData.invoiceItems) {
      return NextResponse.json(
        { error: 'Draft data is required' },
        { status: 400 }
      );
    }

    const userId = authResult.user!.id;

    // البحث عن مسودة موجودة للمستخدم
    const existingDrafts = await prisma.$queryRaw`
      SELECT * FROM sales_order_drafts 
      WHERE "userId" = ${userId}
      LIMIT 1
    `;

    let savedDraft;

    if (Array.isArray(existingDrafts) && existingDrafts.length > 0) {
      // تحديث المسودة الموجودة
      const existingDraft = existingDrafts[0] as any;
      savedDraft = await prisma.$queryRaw`
        UPDATE sales_order_drafts 
        SET "formState" = ${JSON.stringify(draftData.formState || {})},
            "invoiceItems" = ${JSON.stringify(draftData.invoiceItems || [])},
            attachments = ${JSON.stringify(draftData.attachments || [])},
            "soNumber" = ${draftData.soNumber || null},
            "opNumber" = ${draftData.opNumber || null},
            "updatedAt" = CURRENT_TIMESTAMP
        WHERE id = ${existingDraft.id}
        RETURNING *
      `;
    } else {
      // إنشاء مسودة جديدة
      savedDraft = await prisma.$queryRaw`
        INSERT INTO sales_order_drafts ("userId", "formState", "invoiceItems", attachments, "soNumber", "opNumber")
        VALUES (${userId}, ${JSON.stringify(draftData.formState || {})}, ${JSON.stringify(draftData.invoiceItems || [])}, 
                ${JSON.stringify(draftData.attachments || [])}, ${draftData.soNumber || null}, ${draftData.opNumber || null})
        RETURNING *
      `;
    }

    return NextResponse.json({
      success: true,
      draft: savedDraft,
      message: 'تم حفظ مسودة المبيعات في قاعدة البيانات بنجاح'
    });

  } catch (error) {
    console.error('Failed to save sales draft:', error);
    return NextResponse.json(
      { error: 'Failed to save sales draft' },
      { status: 500 }
    );
  }
}

// DELETE - حذف مسودة مبيعات
export async function DELETE(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const draftId = searchParams.get('draftId');
    const userId = authResult.user!.id;

    if (draftId) {
      // حذف مسودة محددة
      await prisma.$queryRaw`
        DELETE FROM sales_order_drafts 
        WHERE id = ${parseInt(draftId)} AND "userId" = ${userId}
      `;
    } else {
      // حذف جميع مسودات المستخدم
      await prisma.$queryRaw`
        DELETE FROM sales_order_drafts 
        WHERE "userId" = ${userId}
      `;
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف مسودة المبيعات بنجاح'
    });

  } catch (error) {
    console.error('Failed to delete sales draft:', error);
    return NextResponse.json(
      { error: 'Failed to delete sales draft' },
      { status: 500 }
    );
  }
}
