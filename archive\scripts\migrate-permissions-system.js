/**
 * سكريپت ترحيل البيانات من النظام القديم إلى النظام المحسن
 * وإزالة حقول permissions و warehouseAccess القديمة
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// جميع الأقسام المتاحة في النظام مع أسمائها المعروضة
const allSections = [
  { name: 'dashboard', displayName: 'الرئيسية' },
  { name: 'clients', displayName: 'العملاء' },
  { name: 'warehouses', displayName: 'إدارة المخازن' },
  { name: 'inventory', displayName: 'المخزون' },
  { name: 'supply', displayName: 'التوريد' },
  { name: 'sales', displayName: 'المبيعات' },
  { name: 'requests', displayName: 'طلبات العملاء' },
  { name: 'returns', displayName: 'المرتجعات' },
  { name: 'maintenance', displayName: 'الصيانة' },
  { name: 'maintenanceTransfer', displayName: 'نقل الصيانة' },
  { name: 'warehouseTransfer', displayName: 'نقل المستودعات' },
  { name: 'grading', displayName: 'الدرجات' },
  { name: 'track', displayName: 'التتبع' },
  { name: 'stocktaking', displayName: 'الجرد' },
  { name: 'acceptDevices', displayName: 'قبول الأجهزة' },
  { name: 'messaging', displayName: 'الرسائل' },
  { name: 'reports', displayName: 'التقارير' },
  { name: 'users', displayName: 'المستخدمين' },
  { name: 'settings', displayName: 'الإعدادات' },
  { name: 'pricing', displayName: 'التسعير' }
];

async function ensurePermissionsExist() {
  console.log('📋 التأكد من وجود جميع الصلاحيات في النظام...');
  
  for (const section of allSections) {
    await prisma.permission.upsert({
      where: { name: section.name },
      update: {
        displayName: section.displayName
      },
      create: {
        name: section.name,
        displayName: section.displayName,
        description: `صلاحية ${section.displayName}`,
        category: 'general'
      }
    });
  }
  
  console.log('✅ تم التأكد من وجود جميع الصلاحيات');
}

async function migrateUserData() {
  console.log('🔄 بدء ترحيل بيانات المستخدمين...');
  
  const users = await prisma.user.findMany({
    where: {
      OR: [
        { permissions: { not: null } },
        { warehouseAccess: { not: null } }
      ]
    }
  });

  console.log(`📊 تم العثور على ${users.length} مستخدم يحتاج إلى ترحيل`);

  for (const user of users) {
    console.log(`\n👤 ترحيل بيانات المستخدم: ${user.name} (ID: ${user.id})`);
    
    try {
      // ترحيل الصلاحيات
      if (user.permissions) {
        let permissions;
        try {
          permissions = typeof user.permissions === 'string' 
            ? JSON.parse(user.permissions) 
            : user.permissions;
        } catch (error) {
          console.warn(`⚠️  فشل في تحليل صلاحيات المستخدم ${user.name}:`, error);
          continue;
        }

        console.log(`  📝 ترحيل ${Object.keys(permissions).length} صلاحية...`);
        
        for (const [permissionName, permissionData] of Object.entries(permissions)) {
          if (typeof permissionData === 'object' && permissionData !== null) {
            // البحث عن الصلاحية في قاعدة البيانات
            const permissionRecord = await prisma.permission.findUnique({
              where: { name: permissionName }
            });
            
            if (permissionRecord) {
              await prisma.userPermission.upsert({
                where: {
                  userId_permissionId: {
                    userId: user.id,
                    permissionId: permissionRecord.id
                  }
                },
                update: {
                  canView: permissionData.view || false,
                  canCreate: permissionData.create || false,
                  canEdit: permissionData.edit || false,
                  canDelete: permissionData.delete || false,
                  canViewAll: permissionData.viewAll || false,
                  canManage: permissionData.manage || false,
                },
                create: {
                  userId: user.id,
                  permissionId: permissionRecord.id,
                  canView: permissionData.view || false,
                  canCreate: permissionData.create || false,
                  canEdit: permissionData.edit || false,
                  canDelete: permissionData.delete || false,
                  canViewAll: permissionData.viewAll || false,
                  canManage: permissionData.manage || false,
                }
              });
              console.log(`    ✅ ${permissionName}`);
            } else {
              console.log(`    ⚠️  صلاحية غير موجودة: ${permissionName}`);
            }
          }
        }
      }

      // ترحيل صلاحيات المخازن
      if (user.warehouseAccess) {
        let warehouseAccess;
        try {
          warehouseAccess = typeof user.warehouseAccess === 'string' 
            ? JSON.parse(user.warehouseAccess) 
            : user.warehouseAccess;
        } catch (error) {
          console.warn(`⚠️  فشل في تحليل صلاحيات المخازن للمستخدم ${user.name}:`, error);
          continue;
        }

        if (Array.isArray(warehouseAccess)) {
          console.log(`  🏬 ترحيل ${warehouseAccess.length} صلاحية مخزن...`);
          
          for (const warehouseId of warehouseAccess) {
            if (typeof warehouseId === 'number') {
              await prisma.userWarehouseAccess.upsert({
                where: {
                  userId_warehouseId: {
                    userId: user.id,
                    warehouseId: warehouseId
                  }
                },
                update: {
                  accessType: 'admin',
                  canTransfer: true,
                  canAudit: true
                },
                create: {
                  userId: user.id,
                  warehouseId: warehouseId,
                  accessType: 'admin',
                  canTransfer: true,
                  canAudit: true
                }
              });
              console.log(`    ✅ مخزن ID: ${warehouseId}`);
            }
          }
        }
      }

      console.log(`✅ تم ترحيل بيانات المستخدم: ${user.name}`);
      
    } catch (error) {
      console.error(`❌ فشل في ترحيل بيانات المستخدم ${user.name}:`, error);
    }
  }
}

async function removeOldFields() {
  console.log('\n🗑️  إزالة الحقول القديمة من قاعدة البيانات...');
  
  try {
    // تحديث جميع المستخدمين لإزالة الحقول القديمة
    await prisma.user.updateMany({
      data: {
        permissions: null,
        warehouseAccess: null
      }
    });
    
    console.log('✅ تم إزالة الحقول القديمة من جميع المستخدمين');
  } catch (error) {
    console.error('❌ فشل في إزالة الحقول القديمة:', error);
  }
}

async function migratePermissions() {
  try {
    console.log('🚀 بدء ترحيل نظام الصلاحيات...');
    
    await ensurePermissionsExist();
    await migrateUserData();
    await removeOldFields();
    
    console.log('\n🎉 تم ترحيل نظام الصلاحيات بنجاح!');
    console.log('📊 ملخص العملية:');
    console.log('  ✅ تم إنشاء جميع الصلاحيات المطلوبة');
    console.log('  ✅ تم ترحيل بيانات المستخدمين');
    console.log('  ✅ تم إزالة الحقول القديمة');
    console.log('  🆕 النظام يعمل الآن بالنظام المحسن فقط');
    
  } catch (error) {
    console.error('💥 فشل في ترحيل نظام الصلاحيات:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// تشغيل السكريپت
if (require.main === module) {
  migratePermissions()
    .then(() => {
      console.log('\n✨ انتهى ترحيل النظام بنجاح!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 فشل في ترحيل النظام:', error);
      process.exit(1);
    });
}

module.exports = { migratePermissions };
