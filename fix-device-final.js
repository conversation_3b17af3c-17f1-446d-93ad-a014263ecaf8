const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixDeviceAndRefresh() {
  try {
    console.log('🔧 إصلاح حالة الجهاز وتحديث البيانات...\n');

    const deviceId = '222222222225555';

    // فحص الحالة الحالية
    const device = await prisma.device.findUnique({
      where: { id: deviceId },
      select: {
        id: true,
        model: true,
        status: true,
        warehouseId: true,
        condition: true,
        price: true
      }
    });

    if (!device) {
      console.log('❌ الجهاز غير موجود');
      return;
    }

    console.log(`📱 حالة الجهاز الحالية:`);
    console.log(`   ID: ${device.id}`);
    console.log(`   الموديل: ${device.model}`);
    console.log(`   الحالة: ${device.status}`);
    console.log(`   المخزن: ${device.warehouseId}`);
    console.log(`   السعر: ${device.price}`);
    console.log(`   الحالة الفيزيائية: ${device.condition}`);

    // تحديث الحالة إلى "متاح للبيع" بدلاً من "متاح"
    const updatedDevice = await prisma.device.update({
      where: { id: deviceId },
      data: {
        status: 'متاح للبيع'
      }
    });

    console.log(`\n✅ تم تحديث حالة الجهاز إلى: ${updatedDevice.status}`);

    // فحص أوامر الصيانة المرتبطة
    const maintenanceOrders = await prisma.maintenanceOrder.findMany({
      where: {
        items: {
          contains: deviceId
        }
      },
      select: {
        id: true,
        orderNumber: true,
        status: true,
        items: true
      }
    });

    console.log(`\n📊 أوامر الصيانة المرتبطة: ${maintenanceOrders.length}`);
    
    if (maintenanceOrders.length > 0) {
      console.log('\n🔧 أوامر الصيانة الموجودة:');
      for (const order of maintenanceOrders) {
        console.log(`   ${order.orderNumber} (ID: ${order.id}) - حالة: ${order.status}`);
        
        // فحص إذا كان هذا الأمر يجب حذفه
        try {
          const items = order.items ? JSON.parse(order.items) : [];
          const deviceInOrder = items.find(item => (item.deviceId || item.id) === deviceId);
          if (deviceInOrder) {
            console.log(`     📱 الجهاز موجود في هذا الأمر`);
            
            // إذا كان الجهاز أصبح متاح للبيع، قد نحتاج لحذف الأمر أو تحديث حالته
            if (order.status === 'wip') {
              console.log(`     ⚠️ الأمر لا يزال نشط - قد تحتاج لحذفه يدوياً`);
            }
          }
        } catch (error) {
          console.log(`     ❌ خطأ في تحليل items: ${error.message}`);
        }
      }
    }

    // فحص المخزن
    if (device.warehouseId) {
      const warehouse = await prisma.warehouse.findUnique({
        where: { id: device.warehouseId },
        select: {
          id: true,
          name: true
        }
      });

      if (warehouse) {
        console.log(`\n🏪 معلومات المخزن:`);
        console.log(`   ID: ${warehouse.id}`);
        console.log(`   الاسم: ${warehouse.name}`);
        
        // عد الأجهزة المتوفرة في هذا المخزن
        const warehouseDevices = await prisma.device.findMany({
          where: {
            warehouseId: device.warehouseId,
            status: {
              in: ['متاح للبيع', 'جديد']
            }
          },
          select: {
            id: true,
            status: true
          }
        });

        console.log(`   الأجهزة المتوفرة: ${warehouseDevices.length}`);
        console.log(`   يتضمن جهازنا: ${warehouseDevices.some(d => d.id === deviceId) ? 'نعم' : 'لا'}`);
      }
    }

    console.log(`\n🎉 تم الانتهاء من الإصلاح. الجهاز الآن متاح للبيع.`);
    console.log(`💡 لحل المشكلة في الواجهة:`);
    console.log(`   1. قم بتحديث الصفحة (F5)`);
    console.log(`   2. أو اذهب لصفحة أخرى وعد لصفحة المخزن`);
    console.log(`   3. أو انتظر تحديث البيانات التلقائي`);

  } catch (error) {
    console.error('خطأ:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixDeviceAndRefresh();
