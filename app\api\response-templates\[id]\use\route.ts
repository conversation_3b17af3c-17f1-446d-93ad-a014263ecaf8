import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const templateId = parseInt(params.id);

    if (isNaN(templateId)) {
      return NextResponse.json({ error: 'Invalid template ID' }, { status: 400 });
    }

    await prisma.responseTemplate.update({
      where: { id: templateId },
      data: {
        usageCount: {
          increment: 1
        }
      }
    });
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('خطأ في تحديث عداد الاستخدام:', error);
    return NextResponse.json({ error: 'Failed to update usage count' }, { status: 500 });
  }
}
