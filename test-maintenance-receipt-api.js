#!/usr/bin/env node

/**
 * سكريبت لاختبار API أوامر استلام الصيانة بعد الإصلاح
 */

const testMaintenanceReceiptAPI = async () => {
  console.log('🧪 اختبار API أوامر استلام الصيانة...\n');

  try {
    // اختبار GET request
    console.log('📤 اختبار جلب أوامر استلام الصيانة:');
    
    const response = await fetch('http://localhost:9005/api/maintenance-receipts', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // في بيئة الإنتاج، ستحتاج لإضافة authentication headers
      }
    });

    console.log(`📥 استجابة الخادم: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ خطأ من الخادم:', errorText);
      
      if (response.status === 500) {
        console.log('\n💡 الحل المقترح:');
        console.log('   • إعادة تشغيل الخادم لتحديث Prisma client cache');
        console.log('   • تشغيل: npm run dev أو yarn dev');
      }
      return;
    }

    const result = await response.json();
    console.log('✅ تم جلب البيانات بنجاح:');
    console.log(`   • عدد أوامر الاستلام: ${result.length}`);
    
    if (result.length > 0) {
      console.log('   • أول أمر:');
      console.log(`     - رقم الإيصال: ${result[0].receiptNumber}`);
      console.log(`     - التاريخ: ${result[0].date}`);
      console.log(`     - الموظف: ${result[0].employeeName}`);
      console.log(`     - الحالة: ${result[0].status || 'غير محدد'}`);
    }

  } catch (error) {
    console.error('❌ خطأ في اختبار API:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 الخادم غير متاح:');
      console.log('   • تأكد من تشغيل الخادم على المنفذ 9005');
      console.log('   • تشغيل: npm run dev أو yarn dev');
    }
  }
};

// تشغيل الاختبار
console.log('⚠️  ملاحظة: هذا الاختبار يتطلب تشغيل خادم التطوير على المنفذ 9005');
console.log('إذا فشل الاختبار، قم بإعادة تشغيل الخادم أولاً\n');

testMaintenanceReceiptAPI();
