const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// محاكاة api-client
const apiClient = {
  get: async (url) => {
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer dXNlcjphZG1pbjphZG1pbg==' // user:admin:admin
    };
    
    const response = await fetch(`http://localhost:9005${url}`, {
      method: 'GET',
      headers
    });
    
    return response;
  }
};

const handleApiResponse = async (response) => {
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Network error' }));
    throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
  }
  
  return response.json();
};

async function testAllAPIs() {
  try {
    console.log('🧪 اختبار جميع APIs شامل...');

    const apiEndpoints = [
      { name: 'الأجهزة', url: '/api/devices' },
      { name: 'المخازن', url: '/api/warehouses' },
      { name: 'المبيعات', url: '/api/sales' },
      { name: 'المرتجعات', url: '/api/returns' },
      { name: 'العملاء', url: '/api/clients' },
      { name: 'الموردين', url: '/api/suppliers' },
      { name: 'أوامر التوريد', url: '/api/supply' },
      { name: 'أوامر التوصيل', url: '/api/delivery-orders' },
      { name: 'نماذج الأجهزة', url: '/api/device-models' },
      { name: 'أوامر الصيانة', url: '/api/maintenance-orders' },
      { name: 'إيصالات الصيانة', url: '/api/maintenance-receipts' },
      { name: 'التقييمات', url: '/api/evaluations' },
      { name: 'الرسائل الداخلية', url: '/api/internal-messages' }
    ];

    console.log(`📋 سيتم اختبار ${apiEndpoints.length} نقطة API...\n`);

    const results = [];

    for (const endpoint of apiEndpoints) {
      console.log(`🔍 اختبار ${endpoint.name} (${endpoint.url})...`);
      
      try {
        const response = await apiClient.get(endpoint.url);
        const data = await handleApiResponse(response);
        
        const count = Array.isArray(data) ? data.length : 1;
        console.log(`✅ ${endpoint.name}: ${count} عنصر`);
        
        results.push({
          name: endpoint.name,
          url: endpoint.url,
          status: 'نجح',
          count,
          error: null
        });
        
      } catch (error) {
        console.log(`❌ ${endpoint.name}: ${error.message}`);
        
        results.push({
          name: endpoint.name,
          url: endpoint.url,
          status: 'فشل',
          count: 0,
          error: error.message
        });
      }
    }

    console.log('\n📊 ملخص النتائج:');
    console.log('==================');
    
    const successful = results.filter(r => r.status === 'نجح');
    const failed = results.filter(r => r.status === 'فشل');
    
    console.log(`✅ APIs ناجحة: ${successful.length}/${results.length}`);
    console.log(`❌ APIs فاشلة: ${failed.length}/${results.length}`);

    if (successful.length > 0) {
      console.log('\n🎉 APIs الناجحة:');
      successful.forEach(result => {
        console.log(`   ✓ ${result.name}: ${result.count} عنصر`);
      });
    }

    if (failed.length > 0) {
      console.log('\n⚠️ APIs الفاشلة:');
      failed.forEach(result => {
        console.log(`   ✗ ${result.name}: ${result.error}`);
      });
    }

    console.log('\n🏁 اكتمل اختبار جميع APIs!');

  } catch (error) {
    console.error('❌ خطأ عام في اختبار APIs:', error);
  }
}

testAllAPIs();
