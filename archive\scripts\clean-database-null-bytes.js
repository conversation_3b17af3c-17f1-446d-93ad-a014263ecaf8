const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function cleanDatabaseFromNullBytes() {
  try {
    console.log('🔍 فحص قاعدة البيانات للبحث عن بيانات تحتوي على null bytes...');

    // دالة تنظيف البيانات
    const sanitizeString = (str) => {
      if (!str || typeof str !== 'string') return str;
      // إزالة null bytes وأحرف التحكم الأخرى
      return str.replace(/\0/g, '').replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '').trim() || null;
    };

    console.log('\n📋 فحص جدول evaluation_orders...');

    // جلب جميع أوامر التقييم
    const evaluationOrders = await prisma.evaluationOrder.findMany({
      include: {
        items: true
      }
    });

    console.log(`📊 تم العثور على ${evaluationOrders.length} أمر تقييم.`);

    let cleanedOrdersCount = 0;
    let cleanedItemsCount = 0;

    for (const order of evaluationOrders) {
      let needsUpdate = false;
      const cleanedData = {};

      // فحص وتنظيف بيانات الأمر
      if (order.orderId && order.orderId !== sanitizeString(order.orderId)) {
        cleanedData.orderId = sanitizeString(order.orderId);
        needsUpdate = true;
      }

      if (order.employeeName && order.employeeName !== sanitizeString(order.employeeName)) {
        cleanedData.employeeName = sanitizeString(order.employeeName);
        needsUpdate = true;
      }

      if (order.notes && order.notes !== sanitizeString(order.notes)) {
        cleanedData.notes = sanitizeString(order.notes);
        needsUpdate = true;
      }

      if (order.status && order.status !== sanitizeString(order.status)) {
        cleanedData.status = sanitizeString(order.status);
        needsUpdate = true;
      }

      if (order.acknowledgedBy && order.acknowledgedBy !== sanitizeString(order.acknowledgedBy)) {
        cleanedData.acknowledgedBy = sanitizeString(order.acknowledgedBy);
        needsUpdate = true;
      }

      if (order.warehouseName && order.warehouseName !== sanitizeString(order.warehouseName)) {
        cleanedData.warehouseName = sanitizeString(order.warehouseName);
        needsUpdate = true;
      }

      // تحديث الأمر إذا لزم الأمر
      if (needsUpdate) {
        console.log(`  🔧 تنظيف أمر التقييم: ${order.orderId}`);
        await prisma.evaluationOrder.update({
          where: { id: order.id },
          data: cleanedData
        });
        cleanedOrdersCount++;
      }

      // فحص وتنظيف عناصر الأمر
      for (const item of order.items) {
        let itemNeedsUpdate = false;
        const cleanedItemData = {};

        const fieldsToCheck = [
          'deviceId', 'model', 'externalGrade', 'screenGrade', 
          'networkGrade', 'finalGrade', 'fault', 'damageType'
        ];

        for (const field of fieldsToCheck) {
          if (item[field] && item[field] !== sanitizeString(item[field])) {
            cleanedItemData[field] = sanitizeString(item[field]);
            itemNeedsUpdate = true;
          }
        }

        if (itemNeedsUpdate) {
          console.log(`    🔧 تنظيف عنصر: ${item.deviceId} - ${item.model}`);
          await prisma.evaluationOrderItem.update({
            where: { id: item.id },
            data: cleanedItemData
          });
          cleanedItemsCount++;
        }
      }
    }

    console.log('\n✅ اكتمل تنظيف قاعدة البيانات!');
    console.log(`📊 إحصائيات التنظيف:`);
    console.log(`   • أوامر تقييم تم تنظيفها: ${cleanedOrdersCount}`);
    console.log(`   • عناصر تم تنظيفها: ${cleanedItemsCount}`);

    if (cleanedOrdersCount === 0 && cleanedItemsCount === 0) {
      console.log('🎉 لا توجد بيانات تحتاج تنظيف. قاعدة البيانات نظيفة!');
    } else {
      console.log('🎉 تم تنظيف جميع البيانات التالفة بنجاح!');
    }

  } catch (error) {
    console.error('❌ خطأ في تنظيف قاعدة البيانات:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// تشغيل السكريبت
cleanDatabaseFromNullBytes();
