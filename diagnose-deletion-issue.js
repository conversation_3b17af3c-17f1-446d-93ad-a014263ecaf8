#!/usr/bin/env node

/**
 * سكريبت لتشخيص مشكلة حذف أوامر الصيانة والتواريخ
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function diagnoseDeletionIssue() {
  console.log('🔍 تشخيص مشكلة حذف أوامر الصيانة...\n');

  try {
    // جلب جميع أوامر الصيانة
    const maintenanceOrders = await prisma.maintenanceOrder.findMany({
      orderBy: { id: 'desc' }
    });

    console.log(`📋 عدد أوامر الصيانة: ${maintenanceOrders.length}`);
    
    // جلب جميع أوامر التقييم
    let evaluationOrders = [];
    try {
      evaluationOrders = await prisma.evaluationOrder.findMany({
        orderBy: { id: 'desc' }
      });
      console.log(`📊 عدد أوامر التقييم: ${evaluationOrders.length}`);
    } catch (error) {
      console.log('⚠️  جدول أوامر التقييم غير موجود أو فارغ');
    }

    // تحليل العلاقات لكل أمر صيانة
    for (const maintenanceOrder of maintenanceOrders) {
      console.log(`\n🔧 تحليل أمر الصيانة: ${maintenanceOrder.orderNumber}`);
      console.log(`   📅 تاريخ الأمر: ${maintenanceOrder.date}`);
      
      // استخراج معرفات الأجهزة
      let deviceIds = [];
      try {
        const items = JSON.parse(maintenanceOrder.items || '[]');
        deviceIds = items.map(item => item.deviceId || item.id).filter(Boolean);
        console.log(`   📱 الأجهزة: ${deviceIds.join(', ')}`);
      } catch (error) {
        console.log('   ⚠️  خطأ في تحليل الأجهزة');
        continue;
      }

      if (deviceIds.length === 0) {
        console.log('   ✅ لا توجد أجهزة - يمكن الحذف');
        continue;
      }

      // فحص أوامر التقييم المرتبطة
      const relatedEvaluationOrders = evaluationOrders.filter(evalOrder => {
        if (!evalOrder.items) return false;
        
        try {
          const evalItems = typeof evalOrder.items === 'string' 
            ? JSON.parse(evalOrder.items) 
            : evalOrder.items;
          
          if (!Array.isArray(evalItems)) return false;
          
          return evalItems.some(item => {
            const itemDeviceId = item.deviceId || item.id;
            return deviceIds.includes(itemDeviceId);
          });
        } catch (error) {
          return false;
        }
      });

      if (relatedEvaluationOrders.length > 0) {
        console.log(`   📊 أوامر التقييم المرتبطة: ${relatedEvaluationOrders.length}`);
        
        const maintenanceDate = new Date(maintenanceOrder.date);
        
        relatedEvaluationOrders.forEach(evalOrder => {
          const evalDate = new Date(evalOrder.date);
          const isAfter = evalDate > maintenanceDate;
          const timeDiff = Math.abs(evalDate - maintenanceDate) / (1000 * 60 * 60 * 24); // أيام
          
          console.log(`     - ${evalOrder.orderNumber || evalOrder.id}: ${evalOrder.date}`);
          console.log(`       ${isAfter ? '⏰ لاحق' : '⏰ سابق'} بـ ${timeDiff.toFixed(1)} يوم`);
        });
        
        const laterOrders = relatedEvaluationOrders.filter(evalOrder => {
          const evalDate = new Date(evalOrder.date);
          return evalDate > maintenanceDate;
        });
        
        if (laterOrders.length > 0) {
          console.log(`   ❌ لا يمكن الحذف - ${laterOrders.length} أمر تقييم لاحق`);
        } else {
          console.log(`   ✅ يمكن الحذف - جميع أوامر التقييم سابقة`);
        }
      } else {
        console.log('   ✅ لا توجد أوامر تقييم مرتبطة - يمكن الحذف');
      }
    }

    // إحصائيات عامة
    console.log('\n📊 إحصائيات:');
    if (maintenanceOrders.length > 0) {
      const dates = maintenanceOrders.map(o => new Date(o.date));
      const minDate = new Date(Math.min(...dates));
      const maxDate = new Date(Math.max(...dates));
      console.log(`   📅 نطاق التواريخ: ${minDate.toISOString().split('T')[0]} إلى ${maxDate.toISOString().split('T')[0]}`);
    }

    if (evaluationOrders.length > 0) {
      const evalDates = evaluationOrders.map(o => new Date(o.date));
      const minEvalDate = new Date(Math.min(...evalDates));
      const maxEvalDate = new Date(Math.max(...evalDates));
      console.log(`   📊 نطاق تواريخ التقييم: ${minEvalDate.toISOString().split('T')[0]} إلى ${maxEvalDate.toISOString().split('T')[0]}`);
    }

    console.log('\n✅ انتهى التشخيص');

  } catch (error) {
    console.error('❌ خطأ في التشخيص:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// تشغيل التشخيص
if (require.main === module) {
  diagnoseDeletionIssue()
    .then(() => {
      console.log('\n🎉 تم إكمال التشخيص');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 فشل التشخيص:', error);
      process.exit(1);
    });
}

module.exports = { diagnoseDeletionIssue };
