const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testWarehousesAPI() {
  try {
    console.log('🧪 اختبار API المخازن...');

    // اختبار الاتصال بقاعدة البيانات
    const count = await prisma.warehouse.count();
    console.log(`📊 إجمالي المخازن في قاعدة البيانات: ${count}`);

    if (count === 0) {
      console.log('ℹ️ لا توجد مخازن في قاعدة البيانات');
      return;
    }

    // اختبار استرجاع البيانات
    console.log('🔍 اختبار استرجاع المخازن...');
    
    const warehouses = await prisma.warehouse.findMany({
      orderBy: { id: 'desc' }
    });

    console.log(`✅ تم استرجاع ${warehouses.length} مخزن بنجاح`);

    // عرض المخازن
    warehouses.forEach((warehouse, index) => {
      console.log(`\n📋 المخزن ${index + 1}:`);
      console.log(`   ID: ${warehouse.id}`);
      console.log(`   الاسم: ${warehouse.name}`);
      console.log(`   النوع: ${warehouse.type}`);
      console.log(`   الموقع: ${warehouse.location || 'غير محدد'}`);
    });

    console.log('\n🎉 اختبار API المخازن مكتمل بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في اختبار API المخازن:', error);
    console.error('تفاصيل الخطأ:', error.message);
    if (error.stack) {
      console.error('Stack trace:', error.stack);
    }
  } finally {
    await prisma.$disconnect();
  }
}

testWarehousesAPI();
