const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// دالة لتنظيف البيانات من null bytes
function sanitizeString(str) {
  if (!str || typeof str !== 'string') return str;
  return str.replace(/\x00/g, '').replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '').trim();
}

async function cleanNullBytesInDeliveryOrders() {
  try {
    console.log('بدء تنظيف null bytes من جداول أوامر التسليم...');

    // تنظيف جدول delivery orders
    const deliveryOrders = await prisma.deliveryOrder.findMany();
    let cleanedDeliveryOrders = 0;

    for (const order of deliveryOrders) {
      const updates = {};
      let needsUpdate = false;

      // فحص وتنظيف كل حقل نصي
      const fieldsToClean = [
        'deliveryOrderNumber',
        'referenceNumber', 
        'warehouseName',
        'employeeName',
        'notes',
        'status',
        'attachmentName'
      ];

      for (const field of fieldsToClean) {
        if (order[field] && typeof order[field] === 'string') {
          const cleaned = sanitizeString(order[field]);
          if (cleaned !== order[field]) {
            updates[field] = cleaned;
            needsUpdate = true;
            console.log(`تنظيف ${field} في أمر التسليم ${order.id}: "${order[field]}" -> "${cleaned}"`);
          }
        }
      }

      if (needsUpdate) {
        await prisma.deliveryOrder.update({
          where: { id: order.id },
          data: updates
        });
        cleanedDeliveryOrders++;
      }
    }

    // تنظيف جدول delivery order items
    const deliveryOrderItems = await prisma.deliveryOrderItem.findMany();
    let cleanedDeliveryItems = 0;

    for (const item of deliveryOrderItems) {
      const updates = {};
      let needsUpdate = false;

      // فحص وتنظيف كل حقل نصي
      const fieldsToClean = [
        'deviceId',
        'model',
        'result',
        'fault',
        'damage',
        'notes'
      ];

      for (const field of fieldsToClean) {
        if (item[field] && typeof item[field] === 'string') {
          const cleaned = sanitizeString(item[field]);
          if (cleaned !== item[field]) {
            updates[field] = cleaned;
            needsUpdate = true;
            console.log(`تنظيف ${field} في عنصر أمر التسليم ${item.id}: "${item[field]}" -> "${cleaned}"`);
          }
        }
      }

      if (needsUpdate) {
        await prisma.deliveryOrderItem.update({
          where: { id: item.id },
          data: updates
        });
        cleanedDeliveryItems++;
      }
    }

    // تنظيف جدول maintenance logs
    const maintenanceLogs = await prisma.maintenanceLog.findMany();
    let cleanedMaintenanceLogs = 0;

    for (const log of maintenanceLogs) {
      const updates = {};
      let needsUpdate = false;

      // فحص وتنظيف كل حقل نصي
      const fieldsToClean = [
        'deviceId',
        'model',
        'notes',
        'result',
        'status'
      ];

      for (const field of fieldsToClean) {
        if (log[field] && typeof log[field] === 'string') {
          const cleaned = sanitizeString(log[field]);
          if (cleaned !== log[field]) {
            updates[field] = cleaned;
            needsUpdate = true;
            console.log(`تنظيف ${field} في سجل الصيانة ${log.id}: "${log[field]}" -> "${cleaned}"`);
          }
        }
      }

      if (needsUpdate) {
        await prisma.maintenanceLog.update({
          where: { id: log.id },
          data: updates
        });
        cleanedMaintenanceLogs++;
      }
    }

    console.log(`\nنتائج التنظيف:`);
    console.log(`أوامر التسليم المنظفة: ${cleanedDeliveryOrders}`);
    console.log(`عناصر أوامر التسليم المنظفة: ${cleanedDeliveryItems}`);
    console.log(`سجلات الصيانة المنظفة: ${cleanedMaintenanceLogs}`);
    console.log('تم الانتهاء من تنظيف null bytes بنجاح!');

  } catch (error) {
    console.error('خطأ في تنظيف null bytes:', error);
  } finally {
    await prisma.$disconnect();
  }
}

cleanNullBytesInDeliveryOrders();
