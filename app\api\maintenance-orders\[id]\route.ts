import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction } from '@/lib/transaction-utils';

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // التحقق من التفويض - يتطلب صلاحيات إدارية للحذف
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    // انتظار params قبل استخدامها (Next.js 15 requirement)
    const resolvedParams = await params;
    const orderId = parseInt(resolvedParams.id);

    if (!orderId || isNaN(orderId)) {
      return NextResponse.json(
        { error: 'Invalid maintenance order ID' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if maintenance order exists
      const existingOrder = await tx.maintenanceOrder.findUnique({
        where: { id: orderId }
      });

      if (!existingOrder) {
        throw new Error('Maintenance order not found');
      }

      // Parse items if they are stored as JSON string
      let items = [];
      if (existingOrder.items) {
        try {
          items = typeof existingOrder.items === 'string' 
            ? JSON.parse(existingOrder.items)
            : existingOrder.items;
        } catch (error) {
          console.error('Error parsing maintenance order items:', error);
          items = [];
        }
      }

      // Delete the maintenance order
      await tx.maintenanceOrder.delete({
        where: { id: orderId }
      });

      console.log(`Deleted maintenance order: ${existingOrder.orderNumber} by user: ${authResult.user!.username}`);

      return {
        success: true,
        deletedOrder: existingOrder,
        items: items
      };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to delete maintenance order:', error);

    if (error instanceof Error && error.message === 'Maintenance order not found') {
      return NextResponse.json({ error: 'Maintenance order not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to delete maintenance order' }, { status: 500 });
  }
}
