const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testDeliveryOrderCreation() {
  try {
    console.log('اختبار إنشاء أمر تسليم جديد...');

    // الحصول على مخزن
    const warehouse = await prisma.warehouse.findFirst();
    if (!warehouse) {
      console.log('لا يوجد مخازن في النظام');
      return;
    }

    // الحصول على جهاز قيد الصيانة
    const maintenanceDevice = await prisma.device.findFirst({
      where: { status: 'قيد الإصلاح' }
    });

    if (!maintenanceDevice) {
      console.log('لا توجد أجهزة قيد الصيانة للاختبار');
      return;
    }

    // بيانات أمر التسليم
    const deliveryOrderData = {
      date: new Date().toISOString(),
      warehouseId: warehouse.id,
      employeeName: 'مختبر النظام',
      notes: 'اختبار إنشاء أمر تسليم',
      items: [
        {
          deviceId: maintenanceDevice.id,
          model: maintenanceDevice.model || 'غير محدد',
          result: 'تم الإصلاح بنجاح',
          fault: 'مشكلة في البرمجيات',
          damage: null,
          notes: 'تم إصلاح الجهاز وجاهز للتسليم'
        }
      ]
    };

    console.log('بيانات أمر التسليم:', JSON.stringify(deliveryOrderData, null, 2));

    // محاولة إنشاء أمر التسليم عبر API
    const response = await fetch('http://localhost:3000/api/delivery-orders', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token' // يجب إضافة token صحيح في حالة الحاجة
      },
      body: JSON.stringify(deliveryOrderData)
    });

    if (response.ok) {
      const result = await response.json();
      console.log('✅ تم إنشاء أمر التسليم بنجاح!');
      console.log('تفاصيل أمر التسليم:', result);
    } else {
      const error = await response.text();
      console.log('❌ فشل في إنشاء أمر التسليم:');
      console.log('Status:', response.status);
      console.log('Error:', error);
    }

  } catch (error) {
    console.error('خطأ في الاختبار:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testDeliveryOrderCreation();
