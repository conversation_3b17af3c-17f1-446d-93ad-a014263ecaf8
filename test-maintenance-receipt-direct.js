#!/usr/bin/env node

/**
 * سكريبت لاختبار جدول MaintenanceReceiptOrder مباشرة بدون API
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testMaintenanceReceiptTable() {
  console.log('🧪 اختبار جدول MaintenanceReceiptOrder مباشرة...\n');

  try {
    // اختبار جلب البيانات
    console.log('📋 اختبار جلب أوامر استلام الصيانة:');
    
    const maintenanceReceipts = await prisma.maintenanceReceiptOrder.findMany({
      orderBy: { id: 'desc' },
      take: 5
    });
    
    console.log(`   ✅ تم جلب ${maintenanceReceipts.length} أمر استلام بنجاح`);

    // اختبار إنشاء أمر استلام جديد
    console.log('\n🆕 اختبار إنشاء أمر استلام جديد:');
    
    const testReceiptOrder = await prisma.maintenanceReceiptOrder.create({
      data: {
        receiptNumber: `REC-TEST-${Date.now()}`,
        referenceNumber: 'TEST-REF',
        date: new Date(),
        employeeName: 'موظف اختبار',
        maintenanceEmployeeName: 'فني صيانة اختبار',
        items: JSON.stringify([{
          deviceId: 'TEST-DEVICE-123',
          model: 'iPhone Test',
          result: 'تم الإصلاح',
          fault: 'اختبار',
          notes: 'أمر اختبار'
        }]),
        notes: 'أمر اختبار لفحص الجدول',
        status: 'completed',
        attachmentName: null
      }
    });

    console.log(`   ✅ تم إنشاء أمر الاستلام: ${testReceiptOrder.receiptNumber}`);
    console.log(`   • ID: ${testReceiptOrder.id}`);
    console.log(`   • الموظف: ${testReceiptOrder.employeeName}`);
    console.log(`   • فني الصيانة: ${testReceiptOrder.maintenanceEmployeeName}`);
    console.log(`   • الحالة: ${testReceiptOrder.status}`);

    // اختبار التحديث
    console.log('\n✏️  اختبار تحديث أمر الاستلام:');
    
    const updatedOrder = await prisma.maintenanceReceiptOrder.update({
      where: { id: testReceiptOrder.id },
      data: {
        notes: 'تم تحديث ملاحظات الاختبار',
        maintenanceEmployeeName: 'فني صيانة محدث'
      }
    });

    console.log(`   ✅ تم تحديث الأمر: ${updatedOrder.receiptNumber}`);
    console.log(`   • الملاحظات الجديدة: ${updatedOrder.notes}`);
    console.log(`   • فني الصيانة الجديد: ${updatedOrder.maintenanceEmployeeName}`);

    // حذف أمر الاختبار
    console.log('\n🗑️  حذف أمر الاختبار:');
    
    await prisma.maintenanceReceiptOrder.delete({
      where: { id: testReceiptOrder.id }
    });

    console.log(`   ✅ تم حذف أمر الاختبار بنجاح`);

    // إحصائيات نهائية
    console.log('\n📊 إحصائيات نهائية:');
    
    const totalReceipts = await prisma.maintenanceReceiptOrder.count();
    console.log(`   • إجمالي أوامر الاستلام: ${totalReceipts}`);

    const recentReceipts = await prisma.maintenanceReceiptOrder.findMany({
      orderBy: { createdAt: 'desc' },
      take: 3,
      select: {
        receiptNumber: true,
        employeeName: true,
        maintenanceEmployeeName: true,
        status: true,
        createdAt: true
      }
    });

    if (recentReceipts.length > 0) {
      console.log('   • آخر أوامر الاستلام:');
      recentReceipts.forEach(receipt => {
        console.log(`     - ${receipt.receiptNumber} | ${receipt.employeeName} | ${receipt.status}`);
      });
    }

    console.log('\n✅ جميع اختبارات جدول MaintenanceReceiptOrder نجحت!');

  } catch (error) {
    console.error('❌ خطأ في اختبار الجدول:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// تشغيل الاختبار
if (require.main === module) {
  testMaintenanceReceiptTable()
    .then(() => {
      console.log('\n🎉 تم إكمال جميع الاختبارات بنجاح!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 فشلت الاختبارات:', error);
      process.exit(1);
    });
}

module.exports = { testMaintenanceReceiptTable };
