const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function recreateMaintenanceLogTable() {
  try {
    console.log('🔄 Recreating maintenance_logs table with correct types...');
    
    // احتفظ بالبيانات الموجودة إذا لزم الأمر
    console.log('📊 Checking existing data...');
    const existingCount = await prisma.$queryRaw`SELECT COUNT(*) FROM maintenance_logs`;
    console.log(`Found ${existingCount[0].count} existing maintenance logs`);
    
    if (existingCount[0].count > 0) {
      console.log('⚠️ Found existing data. Would you like to proceed? (This will delete all data)');
      console.log('   Since this is a schema fix, proceeding...');
    }
    
    // حذف الجدول وإعادة إنشاؤه
    console.log('🗑️  Dropping maintenance_logs table...');
    await prisma.$executeRaw`DROP TABLE IF EXISTS maintenance_logs CASCADE`;
    
    console.log('🔨 Creating maintenance_logs table with correct schema...');
    await prisma.$executeRaw`
      CREATE TABLE maintenance_logs (
        id SERIAL PRIMARY KEY,
        "deviceId" VARCHAR(255) NOT NULL,
        model VARCHAR(255) NOT NULL,
        "repairDate" TIMESTAMP NOT NULL,
        notes TEXT,
        result TEXT,
        status VARCHAR(50) NOT NULL DEFAULT 'pending',
        "acknowledgedDate" TIMESTAMP,
        "warehouseName" VARCHAR(255),
        "acknowledgedBy" VARCHAR(255),
        "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
      )
    `;
    
    console.log('✅ Table recreated successfully!');
    
    // اختبار إنشاء سجل
    console.log('🧪 Testing maintenance log creation...');
    const testLog = await prisma.maintenanceLog.create({
      data: {
        deviceId: 'TEST-DEVICE',
        model: 'Test Model',
        repairDate: new Date(),
        notes: 'Test maintenance log',
        result: 'Test completed',
        status: 'pending'
      }
    });
    
    console.log('✅ Test log created successfully:', testLog.id);
    
    // حذف السجل التجريبي
    await prisma.maintenanceLog.delete({
      where: { id: testLog.id }
    });
    
    console.log('🧹 Test log cleaned up');
    console.log('🎉 Maintenance logs table is now ready!');
    
  } catch (error) {
    console.error('❌ Error recreating table:', error.message);
    if (error.code) {
      console.error('💾 Error code:', error.code);
    }
  } finally {
    await prisma.$disconnect();
  }
}

recreateMaintenanceLogTable();
