# إصلاح مشكلة الأقسام في إدارة المستخدمين

## 📋 وصف المشكلة

في صفحة إدارة المستخدمين، عند إنشاء أو تعديل مستخدم في تبويب الصلاحيات، كانت بعض الأقسام تظهر بـ"(غير متاح)" ولا يمكن تفعيلها حتى لو كان المستخدم لديه صلاحيات تعديل في قسم إدارة المستخدمين.

### السلوك الخاطئ السابق:
- الأقسام مثل "نقل المستودعات" و "الدرجات" تظهر بـ"(غير متاح)"
- لا يمكن تفعيل هذه الأقسام حتى لو كان المستخدم مديراً أو لديه صلاحيات تعديل في قسم المستخدمين
- الكود كان يتحقق من صلاحيات المستخدم الحالي على كل قسم بشكل منفصل

## 🔧 الحل المطبق

تم تعديل منطق التحقق من الصلاحيات في ملف `components/permissions-section.tsx`:

### 1. تحديث منطق `handleSectionToggle`:
```typescript
// التحقق من صلاحيات المستخدم الحالي في قسم إدارة المستخدمين
const hasUserManagementAccess = currentUser?.permissions?.users;
const canManageUsers = hasUserManagementAccess?.edit || hasUserManagementAccess?.create || 
                      hasUserManagementAccess?.delete;

// إذا كان القسم محدد مسبقاً، يمكن إزالته دائماً
// إذا كان غير محدد، يجب أن يكون للمستخدم صلاحيات تعديل في إدارة المستخدمين
if (!isCurrentlySelected && !canManageUsers && !isAdmin) {
  alert('ليس لديك صلاحية تعديل المستخدمين لإضافة هذا القسم.');
  return;
}
```

### 2. تحديث منطق عرض الأقسام:
```typescript
// إذا كان القسم محدد مسبقاً، يمكن التفاعل معه (إزالته)
// إذا لم يكن محدد، يتطلب صلاحيات تعديل في إدارة المستخدمين لإضافته
const isAccessible = isSelected || canManageUsers || isAdmin;
```

## ✅ السلوك الجديد الصحيح:

### للمدراء أو المستخدمين الذين لديهم صلاحيات تعديل في قسم "المستخدمين":
- ✅ يمكن إضافة أي قسم للمستخدمين الآخرين
- ✅ يمكن إزالة أي قسم محدد مسبقاً
- ✅ جميع الأقسام تظهر كمتاحة (بدون "غير متاح")

### للمستخدمين الذين ليس لديهم صلاحيات تعديل في قسم "المستخدمين":
- ❌ لا يمكن إضافة أقسام جديدة (تظهر "غير متاح")
- ✅ يمكن إزالة الأقسام المحددة مسبقاً للمستخدم المراد تعديله

## 📝 الملفات المعدلة:
- `components/permissions-section.tsx`

## 🧪 اختبار الإصلاح:

1. قم بتسجيل الدخول كمدير أو مستخدم لديه صلاحيات تعديل في قسم "المستخدمين"
2. انتقل إلى صفحة إدارة المستخدمين
3. اضغط على "إضافة مستخدم جديد" أو "تعديل" مستخدم موجود
4. انتقل إلى تبويب "الصلاحيات"
5. تحقق من أن جميع الأقسام تظهر كمتاحة ويمكن تفعيلها/إلغاؤها

## 📊 تأثير الإصلاح:

- ✅ حل مشكلة اختفاء الأقسام وعدم إمكانية إعادة تفعيلها
- ✅ تحسن تجربة المستخدم في إدارة الصلاحيات
- ✅ سلوك أكثر منطقية ومرونة
- ✅ احترام التسلسل الهرمي للصلاحيات (صلاحية تعديل المستخدمين تعطي حق إدارة جميع أقسام المستخدمين الآخرين)
