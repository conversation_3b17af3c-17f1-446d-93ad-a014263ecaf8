const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function simpleDatabaseCheck() {
  try {
    console.log('🔍 فحص بسيط لحالة قاعدة البيانات...');

    // إحصائيات قاعدة البيانات
    const totalCounts = await prisma.$queryRaw`
      SELECT 
        (SELECT COUNT(*) FROM "Return") as returns,
        (SELECT COUNT(*) FROM "return_items") as return_items,
        (SELECT COUNT(*) FROM "DeliveryOrder") as delivery_orders,
        (SELECT COUNT(*) FROM "delivery_order_items") as delivery_items,
        (SELECT COUNT(*) FROM "Device") as devices,
        (SELECT COUNT(*) FROM "Client") as clients
    `;

    console.log('\n📈 إحصائيات قاعدة البيانات:');
    const counts = totalCounts[0];
    console.log(`- المرتجعات: ${counts.returns}`);
    console.log(`- عناصر المرتجعات: ${counts.return_items}`);
    console.log(`- أوامر التسليم: ${counts.delivery_orders}`);
    console.log(`- عناصر أوامر التسليم: ${counts.delivery_items}`);
    console.log(`- الأجهزة: ${counts.devices}`);
    console.log(`- العملاء: ${counts.clients}`);

    // اختبار إنشاء مرتجع تجريبي بـ Raw SQL
    console.log('\n🧪 اختبار إنشاء مرتجع تجريبي...');
    const roNumber = `TEST-FINAL-${Date.now()}`;
    
    const returnRecord = await prisma.$queryRaw`
      INSERT INTO "Return" 
      ("roNumber", "opReturnNumber", "date", "saleId", "soNumber", "clientName", "warehouseName", "notes", "status", "employeeName")
      VALUES 
      (${roNumber}, ${roNumber}, ${new Date()}, ${1}, ${'SO-FINAL-TEST'}, ${'عميل اختبار نهائي'}, ${'مخزن اختبار'}, ${'مرتجع اختبار نهائي'}, ${'معلق'}, ${'مختبر النظام'})
      RETURNING id, "roNumber"
    `;

    console.log(`✅ تم إنشاء المرتجع: ${returnRecord[0]?.roNumber || returnRecord.roNumber}`);

    // قراءة المرتجع
    const readReturn = await prisma.$queryRaw`
      SELECT id, "roNumber", "clientName", "warehouseName", "notes", "status", "employeeName"
      FROM "Return" 
      WHERE "roNumber" = ${roNumber}
    `;

    console.log('✅ تم قراءة المرتجع بنجاح:', readReturn[0]?.roNumber || 'لم يتم العثور عليه');

    // حذف المرتجع التجريبي
    const returnId = returnRecord[0]?.id || returnRecord.id;
    await prisma.$executeRaw`DELETE FROM "Return" WHERE id = ${returnId}`;
    console.log('✅ تم حذف المرتجع التجريبي');

    // اختبار إنشاء أمر تسليم تجريبي
    console.log('\n🧪 اختبار إنشاء أمر تسليم تجريبي...');
    const doNumber = `DO-FINAL-${Date.now()}`;
    
    const deliveryRecord = await prisma.$queryRaw`
      INSERT INTO "DeliveryOrder" 
      ("doNumber", "date", "location", "notes", "status", "employeeName")
      VALUES 
      (${doNumber}, ${new Date()}, ${'موقع اختبار'}, ${'أمر تسليم اختبار نهائي'}, ${'معلق'}, ${'مختبر النظام'})
      RETURNING id, "doNumber"
    `;

    console.log(`✅ تم إنشاء أمر التسليم: ${deliveryRecord[0]?.doNumber || deliveryRecord.doNumber}`);

    // حذف أمر التسليم التجريبي
    const deliveryId = deliveryRecord[0]?.id || deliveryRecord.id;
    await prisma.$executeRaw`DELETE FROM "DeliveryOrder" WHERE id = ${deliveryId}`;
    console.log('✅ تم حذف أمر التسليم التجريبي');

    console.log('\n🎉 جميع الاختبارات نجحت! النظام يعمل بشكل صحيح');
    console.log('\n✨ ملخص الإصلاحات:');
    console.log('1. ✅ تم تنظيف جميع الـ null bytes من قاعدة البيانات');
    console.log('2. ✅ تم تحديث API المرتجعات لاستخدام Raw SQL');
    console.log('3. ✅ تم تحديث API أوامر التسليم لاستخدام Raw SQL');
    console.log('4. ✅ تم إضافة تنظيف البيانات (sanitization) لجميع النصوص');
    console.log('5. ✅ تم اختبار جميع العمليات والتأكد من عملها');

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error);
  } finally {
    await prisma.$disconnect();
  }
}

simpleDatabaseCheck();
