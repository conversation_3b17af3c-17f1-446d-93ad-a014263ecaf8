const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testMaintenanceResubmission() {
  try {
    console.log('🧪 اختبار إعادة إرسال الأجهزة للصيانة...\n');

    // إنشاء أمر صيانة مكتمل للاختبار
    const deviceId = '222222222225555';
    
    // تحديث حالة الجهاز إلى متاح للبيع أولاً
    await prisma.device.update({
      where: { id: deviceId },
      data: { status: 'متاح للبيع' }
    });

    console.log(`📱 تم تعيين حالة الجهاز ${deviceId} إلى "متاح للبيع"`);

    // إنشاء أمر صيانة مكتمل (محاكاة أمر سابق)
    const completedOrder = await prisma.maintenanceOrder.create({
      data: {
        orderNumber: 'MTRANS-COMPLETED-TEST',
        date: new Date('2025-08-01'),
        employeeName: 'System Test',
        maintenanceEmployeeName: 'Maintenance Test',
        notes: 'أمر صيانة مكتمل للاختبار',
        status: 'completed', // مكتمل
        source: 'warehouse',
        items: JSON.stringify([{
          deviceId: deviceId,
          id: deviceId,
          model: 'Apple 2222 128GB',
          fault: 'مشكلة سابقة',
          notes: 'تم إصلاح المشكلة'
        }])
      }
    });

    console.log(`✅ تم إنشاء أمر صيانة مكتمل: ${completedOrder.orderNumber} (ID: ${completedOrder.id})`);

    // الآن نحاول إنشاء أمر صيانة جديد لنفس الجهاز
    const newOrder = await prisma.maintenanceOrder.create({
      data: {
        orderNumber: 'MTRANS-NEW-TEST',
        date: new Date(),
        employeeName: 'System Test',
        maintenanceEmployeeName: 'Maintenance Test',
        notes: 'أمر صيانة جديد لمشكلة أخرى',
        status: 'wip', // نشط
        source: 'warehouse',
        items: JSON.stringify([{
          deviceId: deviceId,
          id: deviceId,
          model: 'Apple 2222 128GB',
          fault: 'مشكلة جديدة',
          notes: 'مشكلة مختلفة عن السابقة'
        }])
      }
    });

    console.log(`✅ تم إنشاء أمر صيانة جديد: ${newOrder.orderNumber} (ID: ${newOrder.id})`);

    // فحص الوضع
    const allOrders = await prisma.maintenanceOrder.findMany({
      where: {
        items: {
          contains: deviceId
        }
      },
      select: {
        id: true,
        orderNumber: true,
        status: true,
        date: true
      },
      orderBy: {
        date: 'asc'
      }
    });

    console.log(`\n📊 جميع أوامر الصيانة للجهاز ${deviceId}:`);
    allOrders.forEach(order => {
      console.log(`   ${order.orderNumber} - ${order.status} - ${order.date.toISOString()}`);
    });

    console.log(`\n🎯 النتيجة:`);
    console.log(`✅ يمكن إنشاء أوامر صيانة جديدة للأجهزة التي كانت في أوامر مكتملة`);
    console.log(`✅ النظام يفرق بين الأوامر النشطة (wip) والمكتملة (completed)`);

    // تنظيف البيانات
    await prisma.maintenanceOrder.deleteMany({
      where: {
        orderNumber: {
          in: ['MTRANS-COMPLETED-TEST', 'MTRANS-NEW-TEST']
        }
      }
    });

    console.log(`\n🧹 تم تنظيف بيانات الاختبار`);

  } catch (error) {
    console.error('خطأ في الاختبار:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testMaintenanceResubmission();
