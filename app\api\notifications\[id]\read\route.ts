import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const notificationId = params.id;

    if (!notificationId) {
      return NextResponse.json({ error: 'Notification ID is required' }, { status: 400 });
    }

    // تحديث حالة الإشعار كمقروء
    const notification = await prisma.notification.update({
      where: { 
        id: parseInt(notificationId),
        userId: authResult.user!.id // التأكد من أن المستخدم يملك الإشعار
      },
      data: { 
        read: true,
        readAt: new Date()
      }
    });

    return NextResponse.json(notification);
  } catch (error) {
    console.error('Failed to mark notification as read:', error);
    
    if (error instanceof Error && error.message.includes('Record to update not found')) {
      return NextResponse.json({ error: 'Notification not found' }, { status: 404 });
    }
    
    return NextResponse.json({ error: 'Failed to update notification' }, { status: 500 });
  }
}
