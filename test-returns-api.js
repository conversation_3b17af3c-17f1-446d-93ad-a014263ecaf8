const fetch = require('node-fetch');

async function testReturnsAPI() {
  try {
    console.log('اختبار API للمرتجعات...');
    
    // ملاحظة: نحتاج لتشغيل الخادم أولاً للاختبار
    const response = await fetch('http://localhost:3000/api/returns', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('Status:', response.status);
    
    if (response.ok) {
      const data = await response.json();
      console.log('عدد المرتجعات:', data.length);
      
      if (data.length > 0) {
        console.log('أول مرتجع:');
        console.log('ID:', data[0].id);
        console.log('Client ID:', data[0].clientId);
        console.log('Date:', data[0].date);
        console.log('Status:', data[0].status);
        console.log('عدد العناصر:', data[0].items ? data[0].items.length : 0);
        console.log('عدد المرفقات:', data[0].attachments ? data[0].attachments.length : 0);
      }
    } else {
      const error = await response.text();
      console.error('خطأ:', error);
    }
  } catch (error) {
    console.error('خطأ في الاتصال:', error.message);
    console.log('تأكد من تشغيل الخادم بـ: npm run dev');
  }
}

testReturnsAPI();
