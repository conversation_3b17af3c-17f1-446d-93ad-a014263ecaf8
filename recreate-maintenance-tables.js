#!/usr/bin/env node

/**
 * سكريبت لإعادة إنشاء قاعدة البيانات بشكل كامل
 * يحذف ويعيد إنشاء الجداول المتأثرة بالبيانات التالفة
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function recreateCorruptedTables() {
  console.log('🔄 بدء إعادة إنشاء الجداول المتضررة...\n');

  try {
    // حفظ البيانات السليمة أولاً
    console.log('💾 حفظ البيانات السليمة:');
    
    let devicesBackup = [];
    let usersBackup = [];
    let maintenanceOrdersBackup = [];

    try {
      devicesBackup = await prisma.device.findMany();
      console.log(`   ✅ تم حفظ ${devicesBackup.length} جهاز`);
    } catch (e) {
      console.log('   ⚠️  فشل في حفظ الأجهزة - ستبدأ بجداول فارغة');
    }

    try {
      usersBackup = await prisma.user.findMany();
      console.log(`   ✅ تم حفظ ${usersBackup.length} مستخدم`);
    } catch (e) {
      console.log('   ⚠️  فشل في حفظ المستخدمين');
    }

    // بدء عملية الإعادة الإنشاء
    console.log('\n🗑️  حذف الجداول المتضررة:');

    // حذف الجداول بالترتيب الصحيح (المراجع أولاً)
    try {
      await prisma.$executeRaw`DROP TABLE IF EXISTS "MaintenanceOrderItem" CASCADE;`;
      console.log('   ✅ تم حذف MaintenanceOrderItem');
    } catch (e) {
      console.log('   ⚠️  MaintenanceOrderItem غير موجود أو فشل الحذف');
    }

    try {
      await prisma.$executeRaw`DROP TABLE IF EXISTS "MaintenanceOrder" CASCADE;`;
      console.log('   ✅ تم حذف MaintenanceOrder');
    } catch (e) {
      console.log('   ⚠️  MaintenanceOrder غير موجود أو فشل الحذف');
    }

    try {
      await prisma.$executeRaw`DROP TABLE IF EXISTS "MaintenanceReceiptOrder" CASCADE;`;
      console.log('   ✅ تم حذف MaintenanceReceiptOrder');
    } catch (e) {
      console.log('   ⚠️  MaintenanceReceiptOrder غير موجود أو فشل الحذف');
    }

    // إعادة تطبيق الـ migrations
    console.log('\n🔧 إعادة تطبيق schema قاعدة البيانات:');
    
    // إنشاء جدول MaintenanceOrder
    const createMaintenanceOrderTable = `
      CREATE TABLE "MaintenanceOrder" (
        "id" SERIAL NOT NULL,
        "orderNumber" TEXT NOT NULL,
        "referenceNumber" TEXT,
        "date" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "employeeName" TEXT NOT NULL,
        "maintenanceEmployeeId" INTEGER,
        "maintenanceEmployeeName" TEXT,
        "items" TEXT NOT NULL DEFAULT '[]',
        "notes" TEXT,
        "status" TEXT NOT NULL DEFAULT 'wip',
        "source" TEXT DEFAULT 'warehouse',
        "attachmentName" TEXT,
        "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT "MaintenanceOrder_pkey" PRIMARY KEY ("id")
      );
    `;

    await prisma.$executeRawUnsafe(createMaintenanceOrderTable);
    console.log('   ✅ تم إنشاء جدول MaintenanceOrder');

    // إنشاء الفهارس
    await prisma.$executeRaw`CREATE UNIQUE INDEX "MaintenanceOrder_orderNumber_key" ON "MaintenanceOrder"("orderNumber");`;
    console.log('   ✅ تم إنشاء فهرس orderNumber');

    // إنشاء جدول MaintenanceOrderItem
    const createMaintenanceOrderItemTable = `
      CREATE TABLE "MaintenanceOrderItem" (
        "id" SERIAL NOT NULL,
        "maintenanceOrderId" INTEGER NOT NULL,
        "deviceId" TEXT NOT NULL,
        "model" TEXT NOT NULL,
        "fault" TEXT,
        "notes" TEXT,
        "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT "MaintenanceOrderItem_pkey" PRIMARY KEY ("id")
      );
    `;

    await prisma.$executeRawUnsafe(createMaintenanceOrderItemTable);
    console.log('   ✅ تم إنشاء جدول MaintenanceOrderItem');

    // إنشاء العلاقات
    await prisma.$executeRaw`ALTER TABLE "MaintenanceOrderItem" ADD CONSTRAINT "MaintenanceOrderItem_maintenanceOrderId_fkey" FOREIGN KEY ("maintenanceOrderId") REFERENCES "MaintenanceOrder"("id") ON DELETE CASCADE ON UPDATE CASCADE;`;
    console.log('   ✅ تم إنشاء العلاقات');

    // إنشاء جدول MaintenanceReceiptOrder
    const createMaintenanceReceiptOrderTable = `
      CREATE TABLE "MaintenanceReceiptOrder" (
        "id" SERIAL NOT NULL,
        "receiptNumber" TEXT NOT NULL,
        "referenceNumber" TEXT,
        "date" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "employeeName" TEXT NOT NULL,
        "maintenanceEmployeeName" TEXT,
        "items" TEXT NOT NULL DEFAULT '[]',
        "notes" TEXT,
        "status" TEXT NOT NULL DEFAULT 'completed',
        "attachmentName" TEXT,
        "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT "MaintenanceReceiptOrder_pkey" PRIMARY KEY ("id")
      );
    `;

    await prisma.$executeRawUnsafe(createMaintenanceReceiptOrderTable);
    console.log('   ✅ تم إنشاء جدول MaintenanceReceiptOrder');

    await prisma.$executeRaw`CREATE UNIQUE INDEX "MaintenanceReceiptOrder_receiptNumber_key" ON "MaintenanceReceiptOrder"("receiptNumber");`;
    console.log('   ✅ تم إنشاء فهرس receiptNumber');

    // إعادة تعيين sequences
    await prisma.$executeRaw`SELECT setval('"MaintenanceOrder_id_seq"', 1, false);`;
    await prisma.$executeRaw`SELECT setval('"MaintenanceOrderItem_id_seq"', 1, false);`;
    await prisma.$executeRaw`SELECT setval('"MaintenanceReceiptOrder_id_seq"', 1, false);`;
    console.log('   ✅ تم إعادة تعيين sequences');

    // اختبار الجداول الجديدة
    console.log('\n🧪 اختبار الجداول الجديدة:');
    
    const testOrder = await prisma.maintenanceOrder.create({
      data: {
        orderNumber: 'MTRANS-1',
        date: new Date().toISOString(),
        employeeName: 'موظف اختبار',
        items: JSON.stringify([]),
        status: 'wip',
        source: 'warehouse'
      }
    });

    console.log(`   ✅ تم إنشاء أمر اختبار: ${testOrder.orderNumber}`);

    // حذف أمر الاختبار
    await prisma.maintenanceOrder.delete({
      where: { id: testOrder.id }
    });
    console.log('   🧹 تم حذف أمر الاختبار');

    console.log('\n✅ تم إكمال إعادة إنشاء الجداول بنجاح!');
    console.log('\nملاحظة: تم حذف جميع بيانات أوامر الصيانة السابقة بسبب التلف.');
    console.log('يمكنك الآن إنشاء أوامر صيانة جديدة بأمان.');

  } catch (error) {
    console.error('❌ خطأ أثناء إعادة إنشاء الجداول:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// تشغيل السكريبت
if (require.main === module) {
  console.log('⚠️  تحذير: هذا السكريبت سيحذف جميع بيانات أوامر الصيانة الموجودة!');
  console.log('هل تريد المتابعة؟ (اضغط Ctrl+C للإلغاء أو انتظر 5 ثوان للمتابعة)');
  
  setTimeout(() => {
    recreateCorruptedTables()
      .then(() => {
        console.log('\n🎉 تم إنجاز إعادة الإنشاء بنجاح!');
        process.exit(0);
      })
      .catch((error) => {
        console.error('\n💥 فشل إعادة الإنشاء:', error);
        process.exit(1);
      });
  }, 5000);
}

module.exports = { recreateCorruptedTables };
