const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkNullBytesInData() {
  try {
    // فحص المخازن
    const warehouses = await prisma.warehouse.findMany();
    console.log('فحص المخازن:');
    warehouses.forEach(w => {
      console.log(`ID: ${w.id}, Name: "${w.name}", Contains null: ${w.name ? w.name.includes('\x00') : false}`);
    });

    // فحص الأجهزة
    const devices = await prisma.device.findMany({ take: 5 });
    console.log('\nفحص الأجهزة:');
    devices.forEach(d => {
      console.log(`ID: ${d.id}, Model: "${d.model}", Contains null: ${d.model ? d.model.includes('\x00') : false}`);
    });

    // اختبار إنشاء أمر تسليم بأبسط البيانات
    console.log('\nاختبار إنشاء أمر تسليم ببيانات نظيفة:');
    
    const deliveryOrderNumber = `TEST-${Date.now()}`;
    const simpleData = {
      deliveryOrderNumber,
      date: new Date(),
      warehouseId: 1,
      warehouseName: 'test warehouse',
      employeeName: 'test employee',
      status: 'completed'
    };

    console.log('البيانات البسيطة:', simpleData);

    const order = await prisma.deliveryOrder.create({
      data: simpleData
    });

    console.log('✅ تم إنشاء أمر التسليم بنجاح:', order.id);

    // حذف أمر التسليم
    await prisma.deliveryOrder.delete({
      where: { id: order.id }
    });

    console.log('✅ تم حذف أمر التسليم');

  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

checkNullBytesInData();
