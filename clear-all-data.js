const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function resetDatabase() {
  try {
    console.log('🗑️ Deleting all data from tables...');

    // حذف البيانات من جميع الجداول بالترتيب الصحيح (العلاقات أولاً)
    const tables = [
      'evaluation_order_items',
      'evaluation_orders',
      'user_permissions',
      'user_warehouse_access',
      'permissions',
      'users',
      // ... إضافة باقي الجداول حسب الحاجة
    ];

    for (const table of tables) {
      try {
        console.log(`🧹 Clearing ${table}...`);
        await prisma.$executeRawUnsafe(`TRUNCATE TABLE "${table}" RESTART IDENTITY CASCADE`);
      } catch (error) {
        console.log(`⚠️ Could not clear ${table}:`, error.message);
      }
    }

    console.log('✅ All data cleared!');

  } catch (error) {
    console.error('❌ Reset failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

resetDatabase();
