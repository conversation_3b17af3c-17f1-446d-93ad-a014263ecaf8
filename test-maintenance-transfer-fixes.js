#!/usr/bin/env node

/**
 * سكريبت لاختبار إصلاحات مناقلات الصيانة
 * يختبر حذف الأوامر وإعادة حالة الأجهزة إلى الحالة الصحيحة
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testMaintenanceTransferFixes() {
  console.log('🧪 بدء اختبار إصلاحات مناقلات الصيانة...\n');

  try {
    // 1. اختبار أرقام الأوامر
    console.log('📋 اختبار أرقام الأوامر:');
    
    const maintenanceOrders = await prisma.maintenanceOrder.findMany({
      where: { source: 'warehouse' },
      select: { id: true, orderNumber: true, source: true }
    });

    const mtransOrders = maintenanceOrders.filter(o => o.orderNumber.startsWith('MTRANS-'));
    const maintOrders = maintenanceOrders.filter(o => o.orderNumber.startsWith('MAINT-'));

    console.log(`   • أوامر بنمط MTRANS-: ${mtransOrders.length}`);
    console.log(`   • أوامر بنمط MAINT- (يجب تحديثها): ${maintOrders.length}`);
    
    if (maintOrders.length > 0) {
      console.log('   ⚠️  يوجد أوامر تحتاج تحديث نمط الترقيم');
      maintOrders.forEach(order => {
        console.log(`      - ${order.orderNumber}`);
      });
    } else {
      console.log('   ✅ جميع أوامر المخزن تستخدم نمط MTRANS-');
    }

    // 2. اختبار حالة الأجهزة
    console.log('\n🔧 اختبار حالة الأجهزة:');
    
    const devices = await prisma.device.findMany({
      where: {
        status: {
          in: [
            'بانتظار استلام في الصيانة',
            'قيد الإصلاح',
            'بانتظار استلام في المخزن'
          ]
        }
      },
      select: { id: true, status: true, model: true }
    });

    console.log(`   • الأجهزة بانتظار استلام في الصيانة: ${devices.filter(d => d.status === 'بانتظار استلام في الصيانة').length}`);
    console.log(`   • الأجهزة قيد الإصلاح: ${devices.filter(d => d.status === 'قيد الإصلاح').length}`);
    console.log(`   • الأجهزة بانتظار استلام في المخزن: ${devices.filter(d => d.status === 'بانتظار استلام في المخزن').length}`);

    // 3. اختبار إمكانية حذف الأوامر
    console.log('\n🗑️  اختبار إمكانية حذف الأوامر:');
    
    for (const order of maintenanceOrders.slice(0, 3)) { // اختبار أول 3 أوامر فقط
      // محاكاة التحقق من إمكانية الحذف
      const orderDevices = await prisma.device.findMany({
        where: {
          id: {
            in: order.items ? JSON.parse(order.items).map(item => item.deviceId || item.id) : []
          }
        },
        select: { id: true, status: true }
      });

      const problematicDevices = orderDevices.filter(device => 
        device.status === 'قيد الإصلاح' || device.status === 'بانتظار استلام في المخزن'
      );

      if (problematicDevices.length > 0) {
        console.log(`   ❌ الأمر ${order.orderNumber}: لا يمكن حذفه (${problematicDevices.length} أجهزة في حالة إشكالية)`);
      } else {
        console.log(`   ✅ الأمر ${order.orderNumber}: يمكن حذفه`);
      }
    }

    // 4. عرض إحصائيات عامة
    console.log('\n📊 إحصائيات عامة:');
    
    const totalMaintenanceOrders = await prisma.maintenanceOrder.count();
    const warehouseMaintenanceOrders = await prisma.maintenanceOrder.count({
      where: { source: 'warehouse' }
    });
    const directMaintenanceOrders = await prisma.maintenanceOrder.count({
      where: { source: 'direct' }
    });

    console.log(`   • إجمالي أوامر الصيانة: ${totalMaintenanceOrders}`);
    console.log(`   • أوامر من المخزن: ${warehouseMaintenanceOrders}`);
    console.log(`   • أوامر مباشرة: ${directMaintenanceOrders}`);

    const totalMaintenanceReceipts = await prisma.maintenanceReceiptOrder.count();
    console.log(`   • أوامر استلام الصيانة: ${totalMaintenanceReceipts}`);

    console.log('\n✅ تم إكمال اختبار إصلاحات مناقلات الصيانة!');

  } catch (error) {
    console.error('❌ خطأ أثناء اختبار إصلاحات مناقلات الصيانة:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// تشغيل السكريبت
if (require.main === module) {
  testMaintenanceTransferFixes()
    .then(() => {
      console.log('\n🎉 تم إنجاز الاختبار بنجاح!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 فشل الاختبار:', error);
      process.exit(1);
    });
}

module.exports = { testMaintenanceTransferFixes };
