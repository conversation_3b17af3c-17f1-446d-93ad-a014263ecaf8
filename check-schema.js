const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkSchema() {
  try {
    console.log('فحص أعمدة جداول قاعدة البيانات...');
    
    // فحص أعمدة جدول DeliveryOrder
    const deliveryColumns = await prisma.$queryRaw`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'DeliveryOrder'
      ORDER BY ordinal_position
    `;
    
    console.log('\nأعمدة جدول DeliveryOrder:');
    deliveryColumns.forEach(row => {
      console.log(`- ${row.column_name}`);
    });

    // فحص أعمدة جدول Return
    const returnColumns = await prisma.$queryRaw`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'Return'
      ORDER BY ordinal_position
    `;
    
    console.log('\nأعمدة جدول Return:');
    returnColumns.forEach(row => {
      console.log(`- ${row.column_name}`);
    });

  } catch (error) {
    console.error('خطأ في فحص Schema:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkSchema();
