# إصلاحات مناقلات الصيانة 

## المشاكل التي تم حلها

### 1. مشكلة إعادة حالة الأجهزة عند حذف أمر الإرسال للصيانة

**المشكلة:** عند حذف أمر إرسال للصيانة، كانت الأجهزة تعود إلى حالة "بانتظار استلام في الصيانة" بدلاً من حالتها السابقة الصحيحة.

**الحل:** تم تعديل دالة `deleteMaintenanceOrder` في `context/store.tsx` لتشمل:
- دالة `getPreviousMaintenanceStatus()` لتحديد الحالة السابقة المناسبة
- إعادة الأجهزة إلى حالة "متاح للبيع" بدلاً من "بانتظار استلام في الصيانة"
- تحسين رسائل النشاط لتوضيح عدد الأجهزة التي تم إعادة حالتها

**التغييرات في الكود:**
```javascript
// قبل الإصلاح:
await updateDeviceStatus(deviceId, "بانتظار استلام في الصيانة");

// بعد الإصلاح:
const previousStatus = getPreviousMaintenanceStatus(device?.status);
if (previousStatus) {
  await updateDeviceStatus(deviceInfo.deviceId, previousStatus);
}
```

### 2. مشكلة تضارب أرقام الأوامر بين الصفحات

**المشكلة:** كانت صفحة `maintenance-transfer` وصفحة `maintenance` تستخدمان نفس نمط الترقيم `MAINT-`، مما يسبب تضارب في الأرقام.

**الحل:** 
- تغيير نمط ترقيم أوامر `maintenance-transfer` من `MAINT-` إلى `MTRANS-`
- تحديث منطق توليد الأرقام للبحث عن نمط `MTRANS-` فقط
- إنشاء سكريبت `fix-maintenance-transfer-numbers.js` لتحديث الأوامر الموجودة

**التغييرات في الكود:**
```javascript
// قبل الإصلاح:
const match = order.orderNumber.match(/MAINT-(\d+)$/);
const newId = `MAINT-${maxOrderNumber + 1}`;

// بعد الإصلاح:
const match = order.orderNumber.match(/MTRANS-(\d+)$/);
const newId = `MTRANS-${maxOrderNumber + 1}`;
```

### 3. تحسين دالة فحص إمكانية حذف الأوامر

**المشكلة:** لم تكن دالة `checkMaintenanceOrderRelations` تفحص حالة الأجهزة بشكل صحيح.

**الحل:** تم تحسين الدالة لتشمل:
- فحص حالة الأجهزة قبل السماح بالحذف
- منع حذف الأوامر التي بها أجهزة قيد الإصلاح أو عائدة من الصيانة
- رسائل خطأ واضحة تبين سبب عدم إمكانية الحذف

**التغييرات في الكود:**
```javascript
// إضافة فحص حالة الأجهزة:
const devicesInMaintenance = devicesInOrder.filter(device => 
  device.status === 'قيد الإصلاح' || device.status === 'بانتظار استلام في المخزن'
);

if (devicesInMaintenance.length > 0) {
  return {
    canDelete: false,
    reason: `لا يمكن حذف أمر الصيانة لوجود أجهزة في حالة إشكالية`,
    relatedOperations: statusMessages
  };
}
```

## الملفات المتأثرة

1. **`context/store.tsx`**
   - `deleteMaintenanceOrder()` - إصلاح إعادة حالة الأجهزة
   - `checkMaintenanceOrderRelations()` - تحسين فحص الحذف
   - `getPreviousMaintenanceStatus()` - دالة جديدة لتحديد الحالة السابقة

2. **`app/(main)/maintenance-transfer/page.tsx`**
   - تغيير نمط الترقيم من `MAINT-` إلى `MTRANS-`
   - تحديث منطق توليد الأرقام
   - تحديث رسائل التحقق من التكرار

3. **ملفات السكريبت الجديدة:**
   - `fix-maintenance-transfer-numbers.js` - إصلاح الأرقام الموجودة
   - `test-maintenance-transfer-fixes.js` - اختبار الإصلاحات

## كيفية تشغيل الإصلاحات

### إصلاح أرقام الأوامر الموجودة:
```bash
node fix-maintenance-transfer-numbers.js
```

### اختبار الإصلاحات:
```bash
node test-maintenance-transfer-fixes.js
```

## النتائج

- ✅ تم إصلاح مشكلة إعادة حالة الأجهزة عند الحذف
- ✅ تم حل تضارب أرقام الأوامر (MTRANS- vs MAINT-)
- ✅ تم تحسين فحص إمكانية حذف الأوامر
- ✅ تم تحديث 2 أمر موجود من MAINT- إلى MTRANS-
- ✅ النظام يعمل بشكل صحيح الآن

## اختبارات إضافية مطلوبة

1. اختبار حذف أمر بأجهزة قيد الإصلاح (يجب أن يفشل)
2. اختبار حذف أمر بأجهزة عائدة من الصيانة (يجب أن يفشل)
3. اختبار حذف أمر بأجهزة بانتظار الاستلام في الصيانة (يجب أن ينجح)
4. اختبار إنشاء أوامر جديدة للتأكد من عدم تضارب الأرقام

## ملاحظات مهمة

- يجب تشغيل سكريبت `fix-maintenance-transfer-numbers.js` مرة واحدة فقط على قاعدة البيانات
- في حالة وجود بيانات مهمة، يُنصح بأخذ نسخة احتياطية قبل تشغيل السكريبت
- التغييرات متوافقة مع الإصدارات السابقة ولا تؤثر على البيانات الموجودة
