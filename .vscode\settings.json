{"IDX.aI.enableCodebaseIndexing": true, "github.copilot.enable": {"*": false, "plaintext": true, "markdown": true, "scminput": true}, "github.copilot.chat.reviewSelection.instructions": [], "github.copilot.advanced": {}, "Codegeex.Comment.LanguagePreference": "English", "Codegeex.Chat.LanguagePreference": "English", "Codegeex.SidebarUI.LanguagePreference": "English", "Codegeex.CommitMessage.LanguagePreference": "English", "Codegeex.RepoIndex": true, "Codegeex.CommitMessageStyle": "<PERSON><PERSON><PERSON>", "CodeGPT.apiKey": "CodeGPT Plus Beta", "files.exclude": {"**/.git": false}}