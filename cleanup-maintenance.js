const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function cleanupMaintenanceOrders() {
  try {
    console.log('🧹 تنظيف أوامر الصيانة المتبقية...\n');

    // العثور على جميع أوامر الصيانة
    const orders = await prisma.maintenanceOrder.findMany({
      select: {
        id: true,
        orderNumber: true,
        status: true,
        date: true
      }
    });

    console.log(`📊 أوامر الصيانة الموجودة: ${orders.length}`);
    
    if (orders.length > 0) {
      orders.forEach(order => {
        console.log(`   ${order.orderNumber} (ID: ${order.id}) - ${order.status} - ${order.date}`);
      });

      // حذف جميع الأوامر
      const deleted = await prisma.maintenanceOrder.deleteMany({});
      console.log(`\n✅ تم حذف ${deleted.count} أمر صيانة`);
    } else {
      console.log('✅ لا توجد أوامر صيانة للحذف');
    }

    // التأكد من حالة الجهاز
    const device = await prisma.device.findUnique({
      where: { id: '222222222225555' },
      select: { id: true, status: true }
    });

    if (device) {
      console.log(`\n📱 حالة الجهاز ${device.id}: ${device.status}`);
      
      if (device.status !== 'متاح للبيع') {
        await prisma.device.update({
          where: { id: device.id },
          data: { status: 'متاح للبيع' }
        });
        console.log(`✅ تم تحديث حالة الجهاز إلى "متاح للبيع"`);
      }
    }

    console.log(`\n🎉 تم تنظيف النظام بالكامل. الآن يمكن:`);
    console.log(`   ✅ إرسال أي جهاز للصيانة`);
    console.log(`   ✅ إعادة إرسال الأجهزة للصيانة مرة أخرى`);
    console.log(`   ✅ لا توجد أوامر صيانة نشطة`);

  } catch (error) {
    console.error('خطأ:', error);
  } finally {
    await prisma.$disconnect();
  }
}

cleanupMaintenanceOrders();
