const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// دالة لتنظيف البيانات من null bytes
function sanitizeString(str) {
  if (!str || typeof str !== 'string') return str;
  return str.replace(/\x00/g, '').replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '').trim();
}

async function cleanAllNullBytes() {
  try {
    console.log('بدء تنظيف null bytes من جميع الجداول...');

    // تنظيف جدول المخازن
    const warehouses = await prisma.warehouse.findMany();
    let cleanedWarehouses = 0;

    for (const warehouse of warehouses) {
      const updates = {};
      let needsUpdate = false;

      const fieldsToClean = ['name', 'location', 'manager', 'phone', 'notes'];

      for (const field of fieldsToClean) {
        if (warehouse[field] && typeof warehouse[field] === 'string') {
          const cleaned = sanitizeString(warehouse[field]);
          if (cleaned !== warehouse[field]) {
            updates[field] = cleaned;
            needsUpdate = true;
            console.log(`تنظيف ${field} في المخزن ${warehouse.id}: "${warehouse[field]}" -> "${cleaned}"`);
          }
        }
      }

      if (needsUpdate) {
        await prisma.warehouse.update({
          where: { id: warehouse.id },
          data: updates
        });
        cleanedWarehouses++;
      }
    }

    // تنظيف جدول الأجهزة
    const devices = await prisma.device.findMany();
    let cleanedDevices = 0;

    for (const device of devices) {
      const updates = {};
      let needsUpdate = false;

      const fieldsToClean = ['id', 'model', 'status', 'type', 'color', 'notes'];

      for (const field of fieldsToClean) {
        if (device[field] && typeof device[field] === 'string') {
          const cleaned = sanitizeString(device[field]);
          if (cleaned !== device[field]) {
            updates[field] = cleaned;
            needsUpdate = true;
            console.log(`تنظيف ${field} في الجهاز ${device.id}: "${device[field]}" -> "${cleaned}"`);
          }
        }
      }

      if (needsUpdate) {
        await prisma.device.update({
          where: { id: device.id },
          data: updates
        });
        cleanedDevices++;
      }
    }

    // تنظيف جدول المستخدمين
    const users = await prisma.user.findMany();
    let cleanedUsers = 0;

    for (const user of users) {
      const updates = {};
      let needsUpdate = false;

      const fieldsToClean = ['username', 'name', 'email', 'role', 'department'];

      for (const field of fieldsToClean) {
        if (user[field] && typeof user[field] === 'string') {
          const cleaned = sanitizeString(user[field]);
          if (cleaned !== user[field]) {
            updates[field] = cleaned;
            needsUpdate = true;
            console.log(`تنظيف ${field} في المستخدم ${user.id}: "${user[field]}" -> "${cleaned}"`);
          }
        }
      }

      if (needsUpdate) {
        await prisma.user.update({
          where: { id: user.id },
          data: updates
        });
        cleanedUsers++;
      }
    }

    console.log(`\nنتائج التنظيف:`);
    console.log(`المخازن المنظفة: ${cleanedWarehouses}`);
    console.log(`الأجهزة المنظفة: ${cleanedDevices}`);
    console.log(`المستخدمين المنظفين: ${cleanedUsers}`);
    console.log('تم الانتهاء من تنظيف جميع الجداول!');

    // الآن اختبار إنشاء أمر تسليم
    console.log('\nاختبار إنشاء أمر تسليم بعد التنظيف...');
    
    const warehouse = await prisma.warehouse.findFirst();
    const deliveryOrderData = {
      deliveryOrderNumber: `CLEAN-AFTER-${Date.now()}`,
      date: new Date(),
      warehouseId: warehouse.id,
      warehouseName: warehouse.name,
      employeeName: 'مختبر النظام',
      notes: 'اختبار بعد التنظيف',
      status: 'completed'
    };

    const order = await prisma.deliveryOrder.create({
      data: deliveryOrderData
    });

    console.log('✅ تم إنشاء أمر التسليم بنجاح:', order.id);

    // حذف أمر التسليم
    await prisma.deliveryOrder.delete({
      where: { id: order.id }
    });

    console.log('✅ تم حذف أمر التسليم');

  } catch (error) {
    console.error('❌ خطأ في التنظيف:', error);
  } finally {
    await prisma.$disconnect();
  }
}

cleanAllNullBytes();
