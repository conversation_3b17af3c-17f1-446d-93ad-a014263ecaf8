import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';

// أرشفة الطلبات القديمة
export async function POST(request: NextRequest) {
  try {
    const authResult = await requireAuth(request, 'admin');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { daysOld = 90 } = await request.json();
    
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    const result = await prisma.employeeRequest.updateMany({
      where: {
        requestDate: { lt: cutoffDate },
        status: { in: ['تم التنفيذ', 'مرفوض'] },
        isArchived: false
      },
      data: {
        isArchived: true,
        archivedAt: new Date()
      }
    });

    const archivedCount = result.count;
    
    return NextResponse.json({
      success: true,
      archivedCount,
      message: `تم أرشفة ${archivedCount} طلب`
    });
  } catch (error) {
    console.error('خطأ في الأرشفة:', error);
    return NextResponse.json({ error: 'Archive failed' }, { status: 500 });
  }
}

// استرجاع طلب من الأرشيف
export async function PUT(request: NextRequest) {
  try {
    const authResult = await requireAuth(request, 'manager');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { requestId } = await request.json();
    
    if (!requestId) {
      return NextResponse.json({ error: 'Request ID is required' }, { status: 400 });
    }
    
    await prisma.employeeRequest.update({
      where: { id: requestId },
      data: {
        isArchived: false,
        archivedAt: null
      }
    });

    const success = true;
    
    if (success) {
      return NextResponse.json({
        success: true,
        message: 'تم استرجاع الطلب من الأرشيف'
      });
    } else {
      return NextResponse.json({ error: 'Failed to unarchive request' }, { status: 500 });
    }
  } catch (error) {
    console.error('خطأ في استرجاع الطلب:', error);
    return NextResponse.json({ error: 'Unarchive failed' }, { status: 500 });
  }
}

// جلب إحصائيات الأرشيف
export async function GET(request: NextRequest) {
  try {
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const [total, archived, statusStats, priorityStats] = await Promise.all([
      prisma.employeeRequest.count({ where: { isArchived: false } }),
      prisma.employeeRequest.count({ where: { isArchived: true } }),
      prisma.employeeRequest.groupBy({
        by: ['status'],
        where: { isArchived: false },
        _count: { status: true }
      }),
      prisma.employeeRequest.groupBy({
        by: ['priority'],
        where: { isArchived: false },
        _count: { priority: true }
      })
    ]);

    const requestsByStatus: Record<string, number> = {};
    statusStats.forEach(stat => {
      requestsByStatus[stat.status] = stat._count.status;
    });

    const requestsByPriority: Record<string, number> = {};
    priorityStats.forEach(stat => {
      requestsByPriority[stat.priority] = stat._count.priority;
    });

    const stats = {
      totalRequests: total,
      archivedRequests: archived,
      requestsByStatus,
      requestsByPriority
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error('خطأ في جلب إحصائيات الأرشيف:', error);
    return NextResponse.json({ error: 'Failed to fetch archive stats' }, { status: 500 });
  }
}
