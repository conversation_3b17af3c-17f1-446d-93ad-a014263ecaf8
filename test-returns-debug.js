const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testReturnsAPI() {
  try {
    console.log('🧪 اختبار API المرتجعات...');

    // اختبار الاتصال بقاعدة البيانات
    const count = await prisma.return.count();
    console.log(`📊 إجمالي المرتجعات في قاعدة البيانات: ${count}`);

    if (count === 0) {
      console.log('ℹ️ لا توجد مرتجعات في قاعدة البيانات');
      return;
    }

    // اختبار استرجاع البيانات مع معالجة آمنة للتواريخ
    console.log('🔍 اختبار استرجاع المرتجعات...');
    
    const returns = await prisma.$queryRaw`
      SELECT r.*, 
             ARRAY_AGG(
               CASE WHEN ri.id IS NOT NULL THEN
                 JSON_BUILD_OBJECT(
                   'id', ri.id,
                   'deviceId', ri."deviceId",
                   'model', ri.model,
                   'returnReason', ri."returnReason",
                   'replacementDeviceId', ri."replacementDeviceId",
                   'isReplacement', ri."isReplacement",
                   'originalDeviceId', ri."originalDeviceId"
                 )
               END
             ) FILTER (WHERE ri.id IS NOT NULL) as items
      FROM "Return" r
      LEFT JOIN "return_items" ri ON r.id = ri."returnId"
      GROUP BY r.id
      ORDER BY r.id DESC
      LIMIT 5
    `;

    console.log(`✅ تم استرجاع ${returns.length} مرتجع بنجاح`);

    // فحص كل مرتجع للتحقق من التواريخ
    returns.forEach((returnItem, index) => {
      console.log(`\n📋 المرتجع ${index + 1}:`);
      console.log(`   ID: ${returnItem.id}`);
      console.log(`   رقم المرتجع: ${returnItem.roNumber}`);
      console.log(`   التاريخ الخام: ${returnItem.date}`);
      console.log(`   تاريخ المعالجة الخام: ${returnItem.processedDate}`);
      
      // اختبار تحويل التواريخ
      try {
        const dateResult = returnItem.date ? new Date(returnItem.date).toISOString() : null;
        console.log(`   ✅ التاريخ المحول: ${dateResult}`);
      } catch (error) {
        console.log(`   ❌ خطأ في التاريخ: ${error.message}`);
      }

      try {
        const processedDateResult = returnItem.processedDate ? new Date(returnItem.processedDate).toISOString() : null;
        console.log(`   ✅ تاريخ المعالجة المحول: ${processedDateResult}`);
      } catch (error) {
        console.log(`   ❌ خطأ في تاريخ المعالجة: ${error.message}`);
      }
    });

    // محاكاة API response
    console.log('\n🔄 محاكاة معالجة API...');
    
    const safeToISOString = (dateValue) => {
      if (!dateValue) return null;
      try {
        const date = new Date(dateValue);
        if (isNaN(date.getTime())) {
          console.warn('⚠️ قيمة تاريخ غير صالحة:', dateValue);
          return null;
        }
        return date.toISOString();
      } catch (error) {
        console.warn('⚠️ خطأ في معالجة التاريخ:', dateValue, error.message);
        return null;
      }
    };

    const processedReturns = returns.map((returnItem) => {
      return {
        ...returnItem,
        date: safeToISOString(returnItem.date) || new Date().toISOString(),
        processedDate: safeToISOString(returnItem.processedDate),
        items: returnItem.items || []
      };
    });

    console.log(`✅ تم معالجة ${processedReturns.length} مرتجع بنجاح`);
    console.log('\n🎉 اختبار API مكتمل بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في اختبار API:', error);
    console.error('تفاصيل الخطأ:', error.message);
    if (error.stack) {
      console.error('Stack trace:', error.stack);
    }
  } finally {
    await prisma.$disconnect();
  }
}

testReturnsAPI();
