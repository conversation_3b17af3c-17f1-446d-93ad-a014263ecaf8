const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function debugSpecificOrders() {
  try {
    console.log('🔍 البحث عن أمر التقييم رقم 5 وأوامر الصيانة...\n');

    // البحث عن أمر التقييم رقم 5
    const evaluationOrder = await prisma.evaluationOrder.findFirst({
      where: {
        OR: [
          { orderId: '5' },
          { orderId: 'EVAL-5' },
          { id: 5 }
        ]
      },
      include: {
        items: {
          select: {
            deviceId: true,
            id: true
          }
        }
      }
    });

    if (evaluationOrder) {
      console.log('📊 أمر التقييم المتسبب في المشكلة:');
      console.log(`   الرقم: ${evaluationOrder.orderId || evaluationOrder.id}`);
      console.log(`   التاريخ: ${evaluationOrder.date} (${new Date(evaluationOrder.date).toISOString()})`);
      console.log(`   عدد الأجهزة: ${evaluationOrder.items?.length || 0}`);
      if (evaluationOrder.items) {
        console.log('   الأجهزة:', evaluationOrder.items.map(item => item.deviceId || item.id));
      }
      console.log('');
    } else {
      console.log('❌ لم يتم العثور على أمر التقييم رقم 5\n');
    }

    // البحث عن أوامر الصيانة الحديثة
    const maintenanceOrders = await prisma.maintenanceOrder.findMany({
      orderBy: { date: 'desc' },
      take: 10,
      select: {
        id: true,
        orderNumber: true,
        date: true,
        items: true
      }
    });

    console.log('🔧 أوامر الصيانة الحديثة:');
    maintenanceOrders.forEach(order => {
      // تحويل items من JSON string إلى array
      let orderItems = [];
      try {
        orderItems = order.items ? JSON.parse(order.items) : [];
      } catch (error) {
        console.warn(`خطأ في تحويل items للأمر ${order.orderNumber}:`, error);
      }

      console.log(`   ${order.orderNumber || order.id}: ${order.date} (${new Date(order.date).toISOString()})`);
      if (orderItems.length > 0) {
        const deviceIds = orderItems.map(item => item.deviceId || item.id).filter(Boolean);
        console.log(`     الأجهزة: ${deviceIds.join(', ')}`);
      }
      
      // مقارنة مع أمر التقييم
      if (evaluationOrder) {
        const maintenanceDate = new Date(order.date);
        const evalDate = new Date(evaluationOrder.date);
        const isAfter = maintenanceDate > evalDate;
        
        console.log(`     🕐 ${isAfter ? 'بعد' : 'قبل'} أمر التقييم 5`);
        
        // فحص الأجهزة المشتركة
        if (orderItems.length > 0 && evaluationOrder.items) {
          const maintenanceDevices = orderItems.map(item => item.deviceId || item.id).filter(Boolean);
          const evalDevices = evaluationOrder.items.map(item => item.deviceId || item.id);
          const sharedDevices = maintenanceDevices.filter(deviceId => evalDevices.includes(deviceId));
          
          if (sharedDevices.length > 0) {
            console.log(`     🔗 أجهزة مشتركة: ${sharedDevices.join(', ')}`);
            console.log(`     ❗ ${isAfter ? 'يجب منع الحذف' : 'يجب السماح بالحذف'}`);
          }
        }
      }
      console.log('');
    });

    // عرض التواريخ بتنسيق مختلف للتأكد
    if (evaluationOrder) {
      console.log('📅 تحليل التواريخ:');
      console.log(`أمر التقييم 5: ${evaluationOrder.date}`);
      console.log(`  - كـ Date: ${new Date(evaluationOrder.date)}`);
      console.log(`  - كـ ISO: ${new Date(evaluationOrder.date).toISOString()}`);
      console.log(`  - كـ timestamp: ${new Date(evaluationOrder.date).getTime()}`);
      
      maintenanceOrders.slice(0, 3).forEach(order => {
        // تحويل items من JSON string
        let orderItems = [];
        try {
          orderItems = order.items ? JSON.parse(order.items) : [];
        } catch (error) {
          console.warn(`خطأ في تحويل items للأمر ${order.orderNumber}:`, error);
        }

        const maintenanceDate = new Date(order.date);
        const evalDate = new Date(evaluationOrder.date);
        
        console.log(`أمر الصيانة ${order.orderNumber}: ${order.date}`);
        console.log(`  - كـ Date: ${maintenanceDate}`);
        console.log(`  - كـ ISO: ${maintenanceDate.toISOString()}`);
        console.log(`  - كـ timestamp: ${maintenanceDate.getTime()}`);
        console.log(`  - المقارنة: ${maintenanceDate.getTime()} > ${evalDate.getTime()} = ${maintenanceDate > evalDate}`);
      });
    }

  } catch (error) {
    console.error('خطأ:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugSpecificOrders();
