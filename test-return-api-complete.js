const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testReturnAPIComplete() {
  try {
    console.log('اختبار شامل لـ API المرتجعات...');

    // الحصول على جهاز للاختبار
    const device = await prisma.device.findFirst({
      where: { status: 'متاح للبيع' }
    });

    if (!device) {
      console.log('لا توجد أجهزة متاحة للاختبار');
      return;
    }

    console.log(`الجهاز المختار: ${device.id} (${device.model})`);

    // بيانات المرتجع
    const returnData = {
      date: new Date().toISOString(),
      saleId: 1,
      soNumber: 'SO-TEST-123',
      clientName: 'عميل اختبار',
      warehouseName: 'مخزن اختبار',
      notes: 'مرتجع تجريبي للاختبار',
      status: 'معلق',
      employeeName: 'مختبر النظام',
      items: [
        {
          deviceId: device.id,
          model: device.model || 'غير محدد',
          returnReason: 'عيب في الجهاز',
          isReplacement: false
        }
      ]
    };

    console.log('محاولة إنشاء مرتجع...');

    // محاولة إنشاء المرتجع عبر API (محاكاة)
    const response = await fetch('http://localhost:3000/api/returns', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(returnData)
    }).catch(error => {
      console.log('خطأ في الاتصال بـ API:', error.message);
      return null;
    });

    if (response && response.ok) {
      const result = await response.json();
      console.log('✅ تم إنشاء المرتجع بنجاح عبر API!');
      console.log('تفاصيل المرتجع:', result);
    } else if (response) {
      const error = await response.text();
      console.log('❌ فشل في إنشاء المرتجع عبر API:');
      console.log('Status:', response.status);
      console.log('Error:', error);
    } else {
      console.log('⚠️ لا يمكن الوصول إلى API - اختبار مباشر مع قاعدة البيانات...');
      
      // اختبار مباشر مع قاعدة البيانات
      const roNumber = `RO-${Date.now()}`;
      
      const returnRecord = await prisma.$queryRaw`
        INSERT INTO "Return" 
        ("roNumber", "opReturnNumber", "date", "saleId", "soNumber", "clientName", "warehouseName", "notes", "status", "processedBy", "processedDate", "employeeName", "attachments")
        VALUES 
        (${roNumber}, ${roNumber}, ${new Date()}, ${1}, ${'SO-TEST'}, ${'عميل تجريبي'}, ${'مخزن تجريبي'}, ${'مرتجع تجريبي'}, ${'معلق'}, ${null}, ${null}, ${'مختبر النظام'}, ${null})
        RETURNING *
      `.then(result => Array.isArray(result) ? result[0] : result);

      console.log('✅ تم إنشاء المرتجع مباشرة:', returnRecord.roNumber);

      // حذف المرتجع التجريبي
      await prisma.$executeRaw`DELETE FROM "Return" WHERE id = ${returnRecord.id}`;
      console.log('✅ تم حذف المرتجع التجريبي');
    }

    console.log('\n🎉 انتهى الاختبار بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testReturnAPIComplete();
