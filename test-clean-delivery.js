const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testCreateWithProperDate() {
  try {
    console.log('اختبار إنشاء أمر تسليم مع التعامل الصحيح مع DateTime...');

    // الحصول على مخزن
    const warehouse = await prisma.warehouse.findFirst();
    if (!warehouse) {
      console.log('لا يوجد مخازن في النظام');
      return;
    }

    // إنشاء object من نوع Date صحيح
    const currentDate = new Date();
    console.log('التاريخ الحالي:', currentDate.toISOString());

    // بيانات أمر التسليم
    const deliveryOrderData = {
      deliveryOrderNumber: `CLEAN-${Date.now()}`,
      date: currentDate, // استخدام Date object مباشرة
      warehouseId: warehouse.id,
      warehouseName: warehouse.name || 'مخزن غير محدد',
      employeeName: 'مختبر النظام نظيف',
      notes: 'اختبار إنشاء أمر تسليم نظيف',
      status: 'completed'
    };

    console.log('بيانات أمر التسليم:', deliveryOrderData);

    // إنشاء أمر التسليم
    const order = await prisma.deliveryOrder.create({
      data: deliveryOrderData
    });

    console.log('✅ تم إنشاء أمر التسليم بنجاح!');
    console.log('ID:', order.id);
    console.log('رقم الأمر:', order.deliveryOrderNumber);

    // حذف أمر التسليم
    await prisma.deliveryOrder.delete({
      where: { id: order.id }
    });

    console.log('✅ تم حذف أمر التسليم');

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error);
    
    if (error.message.includes('invalid byte sequence')) {
      console.error('المشكلة: null bytes ما زالت موجودة');
    } else if (error.message.includes('DateTime')) {
      console.error('المشكلة: خطأ في تحويل DateTime');
    } else {
      console.error('المشكلة: خطأ آخر');
    }
  } finally {
    await prisma.$disconnect();
  }
}

testCreateWithProperDate();
