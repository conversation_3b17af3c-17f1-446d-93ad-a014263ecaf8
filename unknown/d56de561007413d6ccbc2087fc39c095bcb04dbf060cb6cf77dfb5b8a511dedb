const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// دالة لتنظيف البيانات من null bytes
function sanitizeString(str) {
  if (!str || typeof str !== 'string') return str;
  return str.replace(/\x00/g, '').replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '').trim();
}

// دالة لتنظيف بيانات المرتجع
function sanitizeReturnData(data) {
  const sanitized = {};
  
  for (const [key, value] of Object.entries(data)) {
    if (typeof value === 'string') {
      sanitized[key] = sanitizeString(value);
    } else {
      sanitized[key] = value;
    }
  }
  
  return sanitized;
}

async function testReturnCreation() {
  try {
    console.log('اختبار إنشاء مرتجع جديد...');

    // الحصول على جهاز مباع
    const soldDevice = await prisma.device.findFirst({
      where: { status: 'مباع' }
    });

    if (!soldDevice) {
      console.log('لا توجد أجهزة مباعة للاختبار');
      return;
    }

    console.log(`الجهاز المختار: ${soldDevice.id} (${soldDevice.model})`);

    // بيانات المرتجع مع تنظيف محتمل للـ null bytes
    const rawReturnData = {
      roNumber: `RO-TEST-${Date.now()}`,
      opReturnNumber: `OP-TEST-${Date.now()}`,
      date: new Date(),
      saleId: null,
      soNumber: 'SO-TEST \x00 مع null byte', // إضافة null byte للاختبار
      clientName: 'عميل اختبار \x00\x01\x02 مع أحرف تحكم',
      warehouseName: 'مخزن اختبار',
      notes: 'اختبار إنشاء مرتجع \x00 نظيف',
      status: 'معلق',
      processedBy: null,
      processedDate: null,
      employeeName: 'مختبر النظام',
      attachments: null
    };

    console.log('البيانات الخام قبل التنظيف:');
    for (const [key, value] of Object.entries(rawReturnData)) {
      if (typeof value === 'string') {
        console.log(`${key}: "${value}" (contains null byte: ${value.includes('\x00')})`);
      }
    }

    const sanitizedReturnData = sanitizeReturnData(rawReturnData);

    console.log('\nالبيانات بعد التنظيف:');
    for (const [key, value] of Object.entries(sanitizedReturnData)) {
      if (typeof value === 'string') {
        console.log(`${key}: "${value}" (contains null byte: ${value.includes('\x00')})`);
      }
    }

    // إنشاء المرتجع
    const returnRecord = await prisma.return.create({
      data: sanitizedReturnData
    });

    console.log('\n✅ تم إنشاء المرتجع بنجاح!');
    console.log('ID:', returnRecord.id);
    console.log('رقم المرتجع:', returnRecord.roNumber);

    // إنشاء عنصر مرتجع
    const sanitizedItemData = {
      returnId: returnRecord.id,
      deviceId: sanitizeString(soldDevice.id) || '',
      model: sanitizeString(soldDevice.model || '') || '',
      returnReason: sanitizeString('سبب الإرجاع \x00 مع null byte') || '',
      replacementDeviceId: null,
      isReplacement: false,
      originalDeviceId: null
    };

    console.log('\nبيانات عنصر المرتجع المنظفة:', sanitizedItemData);

    const returnItem = await prisma.returnItem.create({
      data: sanitizedItemData
    });

    console.log('✅ تم إنشاء عنصر المرتجع:', returnItem.id);

    // حذف المرتجع والعنصر
    await prisma.returnItem.delete({
      where: { id: returnItem.id }
    });

    await prisma.return.delete({
      where: { id: returnRecord.id }
    });

    console.log('✅ تم حذف المرتجع التجريبي');
    console.log('\n🎉 تم إنجاز جميع العمليات بنجاح! لا توجد مشاكل في null bytes');

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error);
    
    if (error.message.includes('invalid byte sequence')) {
      console.error('المشكلة: null bytes ما زالت موجودة في البيانات');
    } else {
      console.error('المشكلة: خطأ آخر');
    }
  } finally {
    await prisma.$disconnect();
  }
}

testReturnCreation();
