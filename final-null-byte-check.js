const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function finalNullByteCheck() {
  try {
    console.log('🔍 الفحص النهائي للـ null bytes في قاعدة البيانات...');

    // فحص جداول المرتجعات
    const returnNullBytes = await prisma.$queryRaw`
      SELECT 
        COUNT(*) as count,
        'Return' as table_name
      FROM "Return" 
      WHERE 
        "roNumber" LIKE '%' || chr(0) || '%' OR
        "opReturnNumber" LIKE '%' || chr(0) || '%' OR
        "soNumber" LIKE '%' || chr(0) || '%' OR
        "clientName" LIKE '%' || chr(0) || '%' OR
        "warehouseName" LIKE '%' || chr(0) || '%' OR
        "notes" LIKE '%' || chr(0) || '%' OR
        "employeeName" LIKE '%' || chr(0) || '%'
    `;

    // فحص جداول عناصر المرتجعات
    const returnItemNullBytes = await prisma.$queryRaw`
      SELECT 
        COUNT(*) as count,
        'return_items' as table_name
      FROM "return_items" 
      WHERE 
        "model" LIKE '%' || chr(0) || '%' OR
        "returnReason" LIKE '%' || chr(0) || '%'
    `;

    // فحص جداول أوامر التسليم
    const deliveryOrderNullBytes = await prisma.$queryRaw`
      SELECT 
        COUNT(*) as count,
        'DeliveryOrder' as table_name
      FROM "DeliveryOrder" 
      WHERE 
        "doNumber" LIKE '%' || chr(0) || '%' OR
        "location" LIKE '%' || chr(0) || '%' OR
        "notes" LIKE '%' || chr(0) || '%' OR
        "employeeName" LIKE '%' || chr(0) || '%'
    `;

    // فحص جداول عناصر أوامر التسليم
    const deliveryItemNullBytes = await prisma.$queryRaw`
      SELECT 
        COUNT(*) as count,
        'delivery_order_items' as table_name
      FROM "delivery_order_items" 
      WHERE 
        "model" LIKE '%' || chr(0) || '%' OR
        "assignedTo" LIKE '%' || chr(0) || '%'
    `;

    // فحص جداول الأجهزة
    const deviceNullBytes = await prisma.$queryRaw`
      SELECT 
        COUNT(*) as count,
        'Device' as table_name
      FROM "Device" 
      WHERE 
        "imei" LIKE '%' || chr(0) || '%' OR
        "model" LIKE '%' || chr(0) || '%' OR
        "serialNumber" LIKE '%' || chr(0) || '%' OR
        "status" LIKE '%' || chr(0) || '%' OR
        "location" LIKE '%' || chr(0) || '%' OR
        "assignedTo" LIKE '%' || chr(0) || '%' OR
        "notes" LIKE '%' || chr(0) || '%'
    `;

    // فحص جداول العملاء
    const clientNullBytes = await prisma.$queryRaw`
      SELECT 
        COUNT(*) as count,
        'Client' as table_name
      FROM "Client" 
      WHERE 
        "name" LIKE '%' || chr(0) || '%' OR
        "phone" LIKE '%' || chr(0) || '%' OR
        "email" LIKE '%' || chr(0) || '%' OR
        "address" LIKE '%' || chr(0) || '%' OR
        "notes" LIKE '%' || chr(0) || '%'
    `;

    const results = [
      returnNullBytes[0],
      returnItemNullBytes[0],
      deliveryOrderNullBytes[0],
      deliveryItemNullBytes[0],
      deviceNullBytes[0],
      clientNullBytes[0]
    ];

    console.log('\n📊 نتائج الفحص:');
    results.forEach(result => {
      const count = parseInt(result.count);
      if (count === 0) {
        console.log(`✅ ${result.table_name}: نظيف (${count} null bytes)`);
      } else {
        console.log(`❌ ${result.table_name}: يحتوي على ${count} null bytes`);
      }
    });

    const totalNullBytes = results.reduce((sum, result) => sum + parseInt(result.count), 0);
    
    if (totalNullBytes === 0) {
      console.log('\n🎉 ممتاز! قاعدة البيانات نظيفة تماماً من الـ null bytes');
    } else {
      console.log(`\n⚠️ تحذير: يوجد ${totalNullBytes} null bytes في قاعدة البيانات`);
    }

    // فحص سريع للرقم الإجمالي للسجلات
    const totalCounts = await prisma.$queryRaw`
      SELECT 
        (SELECT COUNT(*) FROM "Return") as returns,
        (SELECT COUNT(*) FROM "return_items") as return_items,
        (SELECT COUNT(*) FROM "DeliveryOrder") as delivery_orders,
        (SELECT COUNT(*) FROM "delivery_order_items") as delivery_items,
        (SELECT COUNT(*) FROM "Device") as devices,
        (SELECT COUNT(*) FROM "Client") as clients
    `;

    console.log('\n📈 إحصائيات قاعدة البيانات:');
    const counts = totalCounts[0];
    console.log(`- المرتجعات: ${counts.returns}`);
    console.log(`- عناصر المرتجعات: ${counts.return_items}`);
    console.log(`- أوامر التسليم: ${counts.delivery_orders}`);
    console.log(`- عناصر أوامر التسليم: ${counts.delivery_items}`);
    console.log(`- الأجهزة: ${counts.devices}`);
    console.log(`- العملاء: ${counts.clients}`);

  } catch (error) {
    console.error('❌ خطأ في الفحص:', error);
  } finally {
    await prisma.$disconnect();
  }
}

finalNullByteCheck();
