#!/usr/bin/env node

/**
 * سكريبت لاختبار API إنشاء أوامر الصيانة
 */

const testMaintenanceOrderAPI = async () => {
  console.log('🧪 اختبار API إنشاء أوامر الصيانة...\n');

  try {
    // محاولة إنشاء أمر صيانة من المخزن
    const testOrder = {
      orderNumber: 'MTRANS-TEST-1',
      referenceNumber: 'TEST-REF',
      date: new Date().toISOString(),
      employeeName: 'موظف اختبار',
      maintenanceEmployeeId: null,
      maintenanceEmployeeName: null,
      items: [
        {
          deviceId: 'TEST-DEVICE-1',
          model: 'iPhone 13',
          fault: 'اختبار',
          notes: 'جهاز اختبار'
        }
      ],
      notes: 'أمر اختبار',
      attachmentName: null,
      status: 'wip',
      source: 'warehouse'
    };

    console.log('📤 إرسال طلب إنشاء أمر صيانة:');
    console.log(JSON.stringify(testOrder, null, 2));

    const response = await fetch('http://localhost:9005/api/maintenance-orders', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // سنحتاج لإضافة authentication headers في بيئة حقيقية
      },
      body: JSON.stringify(testOrder)
    });

    console.log(`📥 استجابة الخادم: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ خطأ من الخادم:', errorText);
      return;
    }

    const result = await response.json();
    console.log('✅ تم إنشاء الأمر بنجاح:');
    console.log(JSON.stringify(result, null, 2));

  } catch (error) {
    console.error('❌ خطأ في اختبار API:', error);
  }
};

// تشغيل الاختبار
testMaintenanceOrderAPI();
