const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testMaintenanceOperations() {
  try {
    console.log('🧪 اختبار عمليات الصيانة بعد تعطيل فحص أوامر التقييم...\n');

    const deviceId = '222222222225555';

    // إنشاء أمر صيانة للاختبار
    const maintenanceOrder = await prisma.maintenanceOrder.create({
      data: {
        orderNumber: 'MTRANS-TEST-1',
        date: new Date(),
        employeeName: 'System Test',
        maintenanceEmployeeName: 'Maintenance Test',
        notes: 'اختبار عمليات الصيانة',
        status: 'wip',
        source: 'warehouse',
        items: JSON.stringify([{
          deviceId: deviceId,
          id: deviceId,
          model: 'Apple 2222 128GB',
          fault: 'مشكلة للاختبار',
          notes: 'اختبار'
        }])
      }
    });

    console.log(`✅ تم إنشاء أمر صيانة للاختبار: ${maintenanceOrder.orderNumber} (ID: ${maintenanceOrder.id})`);

    // تحديث حالة الجهاز
    await prisma.device.update({
      where: { id: deviceId },
      data: { status: 'بانتظار استلام في الصيانة' }
    });

    console.log(`📱 تم تحديث حالة الجهاز إلى: بانتظار استلام في الصيانة`);

    // فحص الوضع الحالي
    console.log(`\n📊 الوضع الحالي:`);

    const evaluationOrders = await prisma.evaluationOrder.findMany({
      where: {
        items: {
          some: {
            deviceId: deviceId
          }
        }
      },
      select: {
        orderId: true,
        date: true,
        status: true
      }
    });

    console.log(`   أوامر التقييم للجهاز: ${evaluationOrders.length}`);
    evaluationOrders.forEach(order => {
      console.log(`     ${order.orderId} - ${order.status} - ${order.date}`);
    });

    const maintenanceOrders = await prisma.maintenanceOrder.findMany({
      where: {
        items: {
          contains: deviceId
        }
      },
      select: {
        orderNumber: true,
        status: true,
        date: true
      }
    });

    console.log(`   أوامر الصيانة للجهاز: ${maintenanceOrders.length}`);
    maintenanceOrders.forEach(order => {
      console.log(`     ${order.orderNumber} - ${order.status} - ${order.date}`);
    });

    console.log(`\n🎯 النتيجة المتوقعة:`);
    console.log(`✅ يجب أن يكون بإمكان حذف أمر الصيانة`);
    console.log(`✅ يجب أن يكون بإمكان استلام الجهاز من الصيانة`);
    console.log(`✅ أوامر التقييم لا تمنع عمليات الصيانة`);

    // تنظيف
    await prisma.maintenanceOrder.delete({
      where: { id: maintenanceOrder.id }
    });

    await prisma.device.update({
      where: { id: deviceId },
      data: { status: 'متاح للبيع' }
    });

    console.log(`\n🧹 تم تنظيف بيانات الاختبار`);

  } catch (error) {
    console.error('خطأ في الاختبار:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testMaintenanceOperations();
