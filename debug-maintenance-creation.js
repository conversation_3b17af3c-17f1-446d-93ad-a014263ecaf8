#!/usr/bin/env node

/**
 * سكريبت لاختبار مشكلة إنشاء أوامر الصيانة من الواجهة
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function debugMaintenanceOrderCreation() {
  console.log('🔍 تشخيص مشكلة إنشاء أوامر الصيانة...\n');

  try {
    // 1. فحص الاتصال بقاعدة البيانات
    console.log('🔗 فحص الاتصال بقاعدة البيانات:');
    const connectionTest = await prisma.$queryRaw`SELECT 1 as test`;
    console.log('✅ الاتصال بقاعدة البيانات يعمل');

    // 2. فحص جدول maintenanceOrder
    console.log('\n📋 فحص جدول maintenanceOrder:');
    const maintenanceOrdersCount = await prisma.maintenanceOrder.count();
    console.log(`   • عدد أوامر الصيانة الموجودة: ${maintenanceOrdersCount}`);

    const latestOrder = await prisma.maintenanceOrder.findFirst({
      orderBy: { id: 'desc' }
    });
    if (latestOrder) {
      console.log(`   • آخر أمر: ${latestOrder.orderNumber} (ID: ${latestOrder.id})`);
    }

    // 3. فحص جدول Device
    console.log('\n📱 فحص جدول Device:');
    const devicesCount = await prisma.device.count();
    console.log(`   • عدد الأجهزة الموجودة: ${devicesCount}`);

    const deviceStatuses = await prisma.device.groupBy({
      by: ['status'],
      _count: true
    });
    console.log('   • توزيع حالات الأجهزة:');
    deviceStatuses.forEach(status => {
      console.log(`     - ${status.status}: ${status._count} جهاز`);
    });

    // 4. محاولة إنشاء أمر صيانة اختبار
    console.log('\n🧪 محاولة إنشاء أمر صيانة اختبار:');
    
    // البحث عن جهاز متاح للاختبار
    const availableDevice = await prisma.device.findFirst({
      where: { 
        status: { in: ['متاح للبيع', 'تحتاج صيانة', 'معيب', 'تالف'] }
      }
    });

    if (!availableDevice) {
      console.log('⚠️  لا توجد أجهزة متاحة للاختبار');
      return;
    }

    console.log(`   • سيتم استخدام الجهاز: ${availableDevice.id} (${availableDevice.model})`);

    // إنشاء أمر اختبار
    const testOrder = await prisma.maintenanceOrder.create({
      data: {
        orderNumber: `MTRANS-TEST-${Date.now()}`,
        referenceNumber: 'TEST-REF',
        date: new Date().toISOString(),
        employeeName: 'موظف اختبار',
        maintenanceEmployeeName: null,
        items: JSON.stringify([{
          deviceId: availableDevice.id,
          model: availableDevice.model
        }]),
        notes: 'أمر اختبار من سكريبت التشخيص',
        status: 'wip',
        source: 'warehouse',
        attachmentName: null
      }
    });

    console.log(`✅ تم إنشاء أمر اختبار بنجاح: ${testOrder.orderNumber}`);

    // تنظيف - حذف أمر الاختبار
    await prisma.maintenanceOrder.delete({
      where: { id: testOrder.id }
    });
    console.log('🧹 تم حذف أمر الاختبار');

    // 5. فحص أذونات قاعدة البيانات
    console.log('\n🔐 فحص أذونات قاعدة البيانات:');
    try {
      await prisma.maintenanceOrder.findMany({ take: 1 });
      console.log('✅ READ - يعمل');
    } catch (e) {
      console.log('❌ READ - فشل');
    }

    try {
      const testCreate = await prisma.maintenanceOrder.create({
        data: {
          orderNumber: `PERM-TEST-${Date.now()}`,
          date: new Date().toISOString(),
          employeeName: 'test',
          items: '[]',
          status: 'wip',
          source: 'warehouse'
        }
      });
      await prisma.maintenanceOrder.delete({ where: { id: testCreate.id } });
      console.log('✅ CREATE/DELETE - يعمل');
    } catch (e) {
      console.log('❌ CREATE/DELETE - فشل:', e.message);
    }

    console.log('\n✅ انتهى التشخيص');

  } catch (error) {
    console.error('❌ خطأ في التشخيص:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// تشغيل التشخيص
if (require.main === module) {
  debugMaintenanceOrderCreation()
    .then(() => {
      console.log('\n🎉 تم إكمال التشخيص');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 فشل التشخيص:', error);
      process.exit(1);
    });
}

module.exports = { debugMaintenanceOrderCreation };
