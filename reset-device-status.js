const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function resetDeviceStatus() {
  try {
    console.log('🔧 إعادة تعيين حالة الجهاز 222222222225555...\n');

    // العثور على الجهاز
    const device = await prisma.device.findUnique({
      where: { id: '222222222225555' },
      select: {
        id: true,
        model: true,
        status: true
      }
    });

    if (!device) {
      console.log('❌ الجهاز غير موجود');
      return;
    }

    console.log(`📱 الجهاز الحالي:`);
    console.log(`   ID: ${device.id}`);
    console.log(`   الموديل: ${device.model}`);
    console.log(`   الحالة الحالية: ${device.status}`);

    // إعادة تعيين الحالة إلى "متاح"
    const updatedDevice = await prisma.device.update({
      where: { id: device.id },
      data: {
        status: 'متاح'
      }
    });

    console.log(`\n✅ تم تحديث حالة الجهاز:`);
    console.log(`   الحالة الجديدة: ${updatedDevice.status}`);

    // فحص أوامر الصيانة المرتبطة
    const maintenanceOrders = await prisma.maintenanceOrder.findMany({
      where: {
        items: {
          contains: device.id
        }
      }
    });

    console.log(`\n📊 أوامر الصيانة المرتبطة: ${maintenanceOrders.length}`);
    
    if (maintenanceOrders.length > 0) {
      console.log('\n🔧 تفاصيل الأوامر المرتبطة:');
      maintenanceOrders.forEach(order => {
        console.log(`   ${order.orderNumber} (ID: ${order.id}) - حالة: ${order.status}`);
      });

      console.log('\n💡 إذا كنت تريدين حذف أمر الصيانة، استخدمي ID: ' + maintenanceOrders[0].id);
    }

  } catch (error) {
    console.error('خطأ:', error);
  } finally {
    await prisma.$disconnect();
  }
}

resetDeviceStatus();
