const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function finalMaintenanceTest() {
  try {
    console.log('🧪 الاختبار النهائي لنظام الصيانة...\n');

    const deviceId = '222222222225555';

    // إنشاء أمر صيانة جديد
    const maintenanceOrder = await prisma.maintenanceOrder.create({
      data: {
        orderNumber: 'MTRANS-FINAL-TEST',
        date: new Date(),
        employeeName: 'System Test',
        maintenanceEmployeeName: 'Maintenance Test',
        notes: 'اختبار نهائي لنظام الصيانة',
        status: 'wip',
        source: 'warehouse',
        items: JSON.stringify([{
          deviceId: deviceId,
          id: deviceId,
          model: 'Apple 2222 128GB',
          fault: 'اختبار نهائي',
          notes: 'يجب أن يعمل بدون مشاكل'
        }])
      }
    });

    console.log(`✅ تم إنشاء أمر صيانة: ${maintenanceOrder.orderNumber} (ID: ${maintenanceOrder.id})`);

    // تحديث حالة الجهاز
    await prisma.device.update({
      where: { id: deviceId },
      data: { status: 'بانتظار استلام في الصيانة' }
    });

    console.log(`📱 تم تحديث حالة الجهاز إلى: بانتظار استلام في الصيانة`);

    // تحديث الأمر إلى مكتمل
    await prisma.maintenanceOrder.update({
      where: { id: maintenanceOrder.id },
      data: { status: 'completed' }
    });

    console.log(`✅ تم تحديث أمر الصيانة إلى: completed`);

    // إعادة تعيين حالة الجهاز
    await prisma.device.update({
      where: { id: deviceId },
      data: { status: 'متاح للبيع' }
    });

    console.log(`📱 تم إعادة تعيين حالة الجهاز إلى: متاح للبيع`);

    // اختبار إنشاء أمر صيانة جديد (إعادة الإرسال)
    const secondOrder = await prisma.maintenanceOrder.create({
      data: {
        orderNumber: 'MTRANS-RESUBMIT-TEST',
        date: new Date(),
        employeeName: 'System Test',
        maintenanceEmployeeName: 'Maintenance Test',
        notes: 'اختبار إعادة الإرسال',
        status: 'wip',
        source: 'warehouse',
        items: JSON.stringify([{
          deviceId: deviceId,
          id: deviceId,
          model: 'Apple 2222 128GB',
          fault: 'مشكلة جديدة مختلفة',
          notes: 'إعادة إرسال للصيانة'
        }])
      }
    });

    console.log(`✅ تم إنشاء أمر صيانة جديد (إعادة إرسال): ${secondOrder.orderNumber} (ID: ${secondOrder.id})`);

    // فحص الوضع النهائي
    const allOrders = await prisma.maintenanceOrder.findMany({
      where: {
        items: {
          contains: deviceId
        }
      },
      select: {
        id: true,
        orderNumber: true,
        status: true,
        date: true
      },
      orderBy: {
        date: 'asc'
      }
    });

    console.log(`\n📊 جميع أوامر الصيانة للجهاز ${deviceId}:`);
    allOrders.forEach(order => {
      console.log(`   ${order.orderNumber} - ${order.status} - ${order.date.toISOString()}`);
    });

    // تحديث الحالة للاختبار
    await prisma.device.update({
      where: { id: deviceId },
      data: { status: 'بانتظار استلام في الصيانة' }
    });

    console.log(`\n🎯 نتائج الاختبار:`);
    console.log(`✅ يمكن إنشاء أوامر صيانة`);
    console.log(`✅ يمكن تحديث حالة الأوامر`);
    console.log(`✅ يمكن إعادة إرسال الأجهزة للصيانة`);
    console.log(`✅ أوامر التقييم لا تتداخل مع الصيانة`);

    console.log(`\n🧹 ترك البيانات للاختبار في الواجهة...`);
    console.log(`💡 يمكنك الآن اختبار حذف/استلام أوامر الصيانة في الواجهة`);

  } catch (error) {
    console.error('خطأ في الاختبار:', error);
  } finally {
    await prisma.$disconnect();
  }
}

finalMaintenanceTest();
