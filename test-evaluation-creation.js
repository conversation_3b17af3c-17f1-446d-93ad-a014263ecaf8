const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testEvaluationCreation() {
  try {
    console.log('🧪 Testing evaluation order creation...');

    // اختبار 1: بيانات بسيطة بالإنجليزية فقط
    console.log('\n1️⃣ Testing with English-only data:');
    const englishData = {
      orderId: 'TEST-ENG-001',
      employeeName: 'Test User',
      date: new Date().toISOString(),
      status: 'pending',
      notes: 'Test notes'
    };

    const englishEval = await prisma.evaluationOrder.create({
      data: englishData
    });
    console.log('✅ English test successful:', englishEval.id);

    // اختبار 2: بيانات عربية بسيطة
    console.log('\n2️⃣ Testing with Arabic data:');
    const arabicData = {
      orderId: 'TEST-AR-001',
      employeeName: 'مستخدم تجريبي',
      date: new Date().toISOString(),
      status: 'معلق',
      notes: 'ملاحظات تجريبية'
    };

    const arabicEval = await prisma.evaluationOrder.create({
      data: arabicData
    });
    console.log('✅ Arabic test successful:', arabicEval.id);

    // اختبار 3: البيانات الفعلية من التطبيق
    console.log('\n3️⃣ Testing with actual app data:');
    const actualData = {
      orderId: 'EVAL-1',
      employeeName: 'System Administrator',
      date: '2025-08-03T01:25:25.563Z',
      notes: null,
      status: 'معلق',
      acknowledgedBy: null,
      acknowledgedDate: null,
      warehouseName: null
    };

    const actualEval = await prisma.evaluationOrder.create({
      data: actualData
    });
    console.log('✅ Actual data test successful:', actualEval.id);

    // تنظيف بيانات الاختبار
    console.log('\n🧹 Cleaning up test data...');
    await prisma.evaluationOrder.delete({ where: { id: englishEval.id } });
    await prisma.evaluationOrder.delete({ where: { id: arabicEval.id } });
    await prisma.evaluationOrder.delete({ where: { id: actualEval.id } });

    console.log('✅ All tests passed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Error details:', {
      message: error.message,
      code: error.code,
      meta: error.meta
    });
  } finally {
    await prisma.$disconnect();
  }
}

testEvaluationCreation();
