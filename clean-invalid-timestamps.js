const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function cleanInvalidTimestamps() {
  try {
    console.log('🧹 تنظيف التواريخ التي تحتوي على ::timestamp...');

    // البحث عن المرتجعات مع التواريخ التي تحتوي على ::timestamp
    const returns = await prisma.$queryRaw`
      SELECT id, "roNumber", "processedDate"
      FROM "Return" 
      WHERE CAST("processedDate" as TEXT) LIKE '%::timestamp%'
    `;

    console.log(`📊 تم العثور على ${returns.length} مرتجع مع تواريخ تحتوي على ::timestamp`);

    if (returns.length === 0) {
      console.log('✅ لم يتم العثور على تواريخ تحتوي على ::timestamp');
      
      // فحص إضافي للتواريخ غير الصالحة
      const invalidReturns = await prisma.$queryRaw`
        SELECT id, "roNumber", "processedDate"
        FROM "Return" 
        WHERE "processedDate" IS NOT NULL
        AND "processedDate"::text ~ '[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}\.[0-9]+\+[0-9]{2}::timestamp'
      `;
      
      console.log(`📊 فحص إضافي: ${invalidReturns.length} تاريخ غير صالح`);
      
      if (invalidReturns.length > 0) {
        console.log('🔧 إصلاح التواريخ غير الصالحة...');
        
        for (const ret of invalidReturns) {
          // تنظيف التاريخ من ::timestamp
          let cleanDate = ret.processedDate;
          if (typeof cleanDate === 'string' && cleanDate.includes('::timestamp')) {
            cleanDate = cleanDate.replace('::timestamp', '');
          }
          
          await prisma.$executeRaw`
            UPDATE "Return" 
            SET "processedDate" = ${cleanDate}::timestamp
            WHERE id = ${ret.id}
          `;
          
          console.log(`✅ تم إصلاح المرتجع: ${ret.roNumber}`);
        }
      }
      
      return;
    }

    // إصلاح التواريخ
    console.log('🔧 إصلاح التواريخ...');
    
    for (const ret of returns) {
      console.log(`📋 معالجة المرتجع: ${ret.roNumber}`);
      console.log(`   التاريخ الأصلي: ${ret.processedDate}`);
      
      // تنظيف التاريخ من ::timestamp
      let cleanDate = ret.processedDate;
      if (typeof cleanDate === 'string' && cleanDate.includes('::timestamp')) {
        cleanDate = cleanDate.replace('::timestamp', '');
      }
      
      console.log(`   التاريخ النظيف: ${cleanDate}`);
      
      try {
        // اختبار صحة التاريخ
        const testDate = new Date(cleanDate);
        if (isNaN(testDate.getTime())) {
          console.log(`   ⚠️ تاريخ غير صالح، سيتم تعيين NULL`);
          await prisma.$executeRaw`
            UPDATE "Return" 
            SET "processedDate" = NULL
            WHERE id = ${ret.id}
          `;
        } else {
          await prisma.$executeRaw`
            UPDATE "Return" 
            SET "processedDate" = ${cleanDate}::timestamp
            WHERE id = ${ret.id}
          `;
        }
        
        console.log(`   ✅ تم إصلاح المرتجع: ${ret.roNumber}`);
      } catch (error) {
        console.log(`   ❌ خطأ في إصلاح التاريخ: ${error.message}`);
        // تعيين NULL في حالة الخطأ
        await prisma.$executeRaw`
          UPDATE "Return" 
          SET "processedDate" = NULL
          WHERE id = ${ret.id}
        `;
        console.log(`   ⚠️ تم تعيين NULL للتاريخ`);
      }
    }

    console.log('\n🎉 تم تنظيف جميع التواريخ بنجاح!');

    // التحقق من النتائج
    const remainingInvalidReturns = await prisma.$queryRaw`
      SELECT id, "roNumber", "processedDate"
      FROM "Return" 
      WHERE CAST("processedDate" as TEXT) LIKE '%::timestamp%'
    `;

    console.log(`\n📊 التحقق النهائي: ${remainingInvalidReturns.length} تاريخ غير صالح متبقي`);

  } catch (error) {
    console.error('❌ خطأ في تنظيف التواريخ:', error);
  } finally {
    await prisma.$disconnect();
  }
}

cleanInvalidTimestamps();
