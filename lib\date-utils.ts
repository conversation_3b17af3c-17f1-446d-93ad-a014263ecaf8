/**
 * Date utility functions for Arabic formatting and manipulation
 * Uses date-fns for consistent date handling across the application
 */

import { format, formatDistanceToNow, isToday as dateFnsIsToday, parseISO } from 'date-fns';
import { ar } from 'date-fns/locale';

// تنسيقات موحدة للتواريخ
export const DATE_FORMATS = {
  // التنسيق الكامل: التاريخ والوقت
  FULL: 'yyyy-MM-dd HH:mm',
  FULL_ARABIC: 'dd MMMM yyyy - HH:mm',
  
  // التاريخ فقط
  DATE_ONLY: 'yyyy-MM-dd',
  DATE_ONLY_ARABIC: 'dd MMMM yyyy',
  DATE_SHORT: 'dd/MM/yyyy',
  
  // الوقت فقط
  TIME_ONLY: 'HH:mm',
  TIME_12H: 'hh:mm a',
  
  // تنسيقات للجداول
  TABLE_DATE: 'dd/MM/yy',
  TABLE_DATETIME: 'dd/MM/yy HH:mm',
  
  // تنسيقات للتصدير
  CSV_FORMAT: 'yyyy-MM-dd',
  ISO_FORMAT: "yyyy-MM-dd'T'HH:mm:ss.SSSxxx"
} as const;

/**
 * Safely parse a date from various input types
 * @param date - Date input (Date, string, or null/undefined)
 * @returns Date object or null if invalid
 */
function safeParseDateNew(date: Date | string | null | undefined): Date | null {
  if (!date) return null;
  
  if (date instanceof Date) {
    return isNaN(date.getTime()) ? null : date;
  }
  
  if (typeof date === 'string') {
    try {
      // Try parsing ISO string first
      if (date.includes('T') || date.includes('-')) {
        const parsed = parseISO(date);
        return isNaN(parsed.getTime()) ? null : parsed;
      }
      // Fallback to regular Date constructor
      const parsed = new Date(date);
      return isNaN(parsed.getTime()) ? null : parsed;
    } catch {
      return null;
    }
  }
  
  return null;
}

/**
 * Format date with a specific pattern
 * @param date - Date input
 * @param pattern - Format pattern from DATE_FORMATS
 * @param options - Additional options
 * @returns Formatted date string or fallback
 */
function formatDateWithPattern(
  date: Date | string | null | undefined, 
  pattern: string, 
  options: { 
    fallback?: string;
    useArabic?: boolean;
  } = {}
): string {
  const { fallback = '--', useArabic = false } = options;
  
  const parsedDate = safeParseDateNew(date);
  if (!parsedDate) return fallback;
  
  try {
    return format(parsedDate, pattern, useArabic ? { locale: ar } : undefined);
  } catch {
    return fallback;
  }
}

// ===== الدوال الرئيسية الموحدة =====

/**
 * Format date and time (الدالة الأساسية للتاريخ والوقت)
 * @param date - Date input
 * @param options - Formatting options
 * @returns Formatted date and time string
 * @example formatDateTime(new Date()) → "2024-08-05 10:30"
 */
export function formatDateTime(
  date: Date | string | null | undefined,
  options: {
    fallback?: string;
    arabic?: boolean;
    format?: 'full' | 'short' | 'table';
  } = {}
): string {
  const { fallback = '--', arabic = false, format: formatType = 'full' } = options;
  
  let pattern: string;
  
  switch (formatType) {
    case 'short':
      pattern = arabic ? 'dd/MM/yy HH:mm' : DATE_FORMATS.TABLE_DATETIME;
      break;
    case 'table':
      pattern = DATE_FORMATS.TABLE_DATETIME;
      break;
    case 'full':
    default:
      pattern = arabic ? DATE_FORMATS.FULL_ARABIC : DATE_FORMATS.FULL;
      break;
  }
  
  return formatDateWithPattern(date, pattern, { fallback, useArabic: arabic });
}

/**
 * Format date only (بدون وقت)
 * @param date - Date input
 * @param options - Formatting options
 * @returns Formatted date string
 * @example formatDate(new Date()) → "2024-08-05"
 */
export function formatDate(
  date: Date | string | null | undefined,
  options: {
    fallback?: string;
    arabic?: boolean;
    format?: 'full' | 'short' | 'table';
  } = {}
): string {
  const { fallback = '--', arabic = false, format: formatType = 'full' } = options;
  
  let pattern: string;
  
  switch (formatType) {
    case 'short':
      pattern = DATE_FORMATS.DATE_SHORT;
      break;
    case 'table':
      pattern = DATE_FORMATS.TABLE_DATE;
      break;
    case 'full':
    default:
      pattern = arabic ? DATE_FORMATS.DATE_ONLY_ARABIC : DATE_FORMATS.DATE_ONLY;
      break;
  }
  
  return formatDateWithPattern(date, pattern, { fallback, useArabic: arabic });
}

/**
 * Format time only (الوقت فقط)
 * @param date - Date input
 * @param options - Formatting options
 * @returns Formatted time string
 * @example formatTime(new Date()) → "10:30"
 */
export function formatTime(
  date: Date | string | null | undefined,
  options: {
    fallback?: string;
    format12h?: boolean;
  } = {}
): string {
  const { fallback = '--', format12h = false } = options;
  
  const pattern = format12h ? DATE_FORMATS.TIME_12H : DATE_FORMATS.TIME_ONLY;
  return formatDateWithPattern(date, pattern, { fallback });
}

// ===== دوال متخصصة =====

/**
 * Format date for table display (للجداول)
 * @param date - Date input
 * @param includeTime - Whether to include time
 * @returns Formatted date string for tables
 */
export function formatTableDate(
  date: Date | string | null | undefined,
  includeTime: boolean = true
): string {
  return includeTime 
    ? formatDateTime(date, { format: 'table' })
    : formatDate(date, { format: 'table' });
}

/**
 * Format date for CSV export (للتصدير)
 * @param date - Date input
 * @returns ISO formatted date string
 */
export function formatDateForCSV(date: Date | string | null | undefined): string {
  return formatDateWithPattern(date, DATE_FORMATS.CSV_FORMAT, { fallback: '' });
}

/**
 * Get relative time in Arabic (الوقت النسبي)
 * @param date - Date input
 * @returns Relative time string in Arabic
 * @example getRelativeTimeArabic(date) → "منذ 5 دقائق"
 */
export function getRelativeTimeArabic(date: Date | string | null | undefined): string {
  const parsedDate = safeParseDateNew(date);
  if (!parsedDate) return '--';
  
  try {
    const relativeTime = formatDistanceToNow(parsedDate, { 
      addSuffix: true,
      locale: ar 
    });
    return relativeTime;
  } catch {
    return formatDate(date, { arabic: true });
  }
}

/**
 * Check if date is today
 * @param date - Date input
 * @returns True if date is today
 */
export function isToday(date: Date | string | null | undefined): boolean {
  const parsedDate = safeParseDateNew(date);
  if (!parsedDate) return false;
  
  try {
    return dateFnsIsToday(parsedDate);
  } catch {
    return false;
  }
}

/**
 * Get current date in Arabic format
 * @returns Current date formatted in Arabic
 */
export function getCurrentArabicDate(): string {
  return formatDate(new Date(), { arabic: true });
}

/**
 * Parse date from various inputs
 * @param input - Date input (string or Date)
 * @returns Parsed date object or current date as fallback
 */
export function parseArabicDate(input: string | Date | null | undefined): Date {
  const parsed = safeParseDateNew(input);
  return parsed || new Date();
}

// ===== الدوال القديمة للتوافق العكسي =====

/**
 * @deprecated Use formatDateTime() instead
 * Format date in Arabic locale
 */
export function formatArabicDate(date: Date | string): string {
  return formatDateTime(date, { arabic: true });
}

/**
 * @deprecated Use formatDate() instead
 * Format date in Arabic locale (date only, no time)
 */
export function formatArabicDateOnly(date: Date | string): string {
  return formatDate(date, { arabic: true });
}

/**
 * @deprecated Use formatTime() instead
 * Format date in Arabic locale (time only)
 */
export function formatArabicTime(date: Date | string): string {
  return formatTime(date);
}

/**
 * @deprecated Use formatDate() instead
 * Format date for display in tables (short format)
 */
export function formatShortDate(date: Date | string): string {
  return formatDate(date, { format: 'short' });
}
