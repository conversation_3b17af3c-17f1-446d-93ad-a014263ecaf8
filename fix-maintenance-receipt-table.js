#!/usr/bin/env node

/**
 * سكريبت لإصلاح جدول MaintenanceReceiptOrder بإضافة جميع الأعمدة المفقودة
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixMaintenanceReceiptOrderTable() {
  console.log('🔧 إصلاح جدول MaintenanceReceiptOrder...\n');

  try {
    // فحص الأعمدة الموجودة حالياً
    console.log('🔍 فحص الأعمدة الموجودة حالياً:');
    
    const currentColumnsQuery = `
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'MaintenanceReceiptOrder'
      ORDER BY ordinal_position;
    `;

    const currentColumns = await prisma.$queryRawUnsafe(currentColumnsQuery);
    console.log('   الأعمدة الموجودة:');
    currentColumns.forEach(col => {
      console.log(`   • ${col.column_name} (${col.data_type})`);
    });

    const existingColumnNames = currentColumns.map(col => col.column_name);

    // قائمة الأعمدة المطلوبة حسب Schema
    const requiredColumns = [
      { name: 'maintenanceEmployeeName', type: 'TEXT', nullable: true },
      { name: 'status', type: 'TEXT', nullable: false, default: "'completed'" }
    ];

    // إضافة الأعمدة المفقودة
    console.log('\n🔧 إضافة الأعمدة المفقودة:');
    
    for (const column of requiredColumns) {
      if (!existingColumnNames.includes(column.name)) {
        console.log(`   ⚡ إضافة ${column.name}...`);
        
        let addColumnQuery = `ALTER TABLE "MaintenanceReceiptOrder" ADD COLUMN "${column.name}" ${column.type}`;
        
        if (!column.nullable) {
          addColumnQuery += ` NOT NULL`;
        }
        
        if (column.default) {
          addColumnQuery += ` DEFAULT ${column.default}`;
        }
        
        addColumnQuery += ';';
        
        await prisma.$executeRawUnsafe(addColumnQuery);
        console.log(`   ✅ تم إضافة ${column.name}`);
      } else {
        console.log(`   ✅ ${column.name} موجود بالفعل`);
      }
    }

    // فحص الجدول بعد التحديث
    console.log('\n📋 هيكل الجدول بعد التحديث:');
    
    const updatedColumns = await prisma.$queryRawUnsafe(currentColumnsQuery);
    updatedColumns.forEach(col => {
      console.log(`   • ${col.column_name} (${col.data_type}) - ${col.is_nullable === 'YES' ? 'NULL' : 'NOT NULL'}`);
    });

    // اختبار عملية جلب البيانات
    console.log('\n🧪 اختبار جلب بيانات MaintenanceReceiptOrder:');
    
    // استخدام raw query لتجنب مشاكل Prisma cache
    const testQuery = `SELECT * FROM "MaintenanceReceiptOrder" LIMIT 5;`;
    const maintenanceReceipts = await prisma.$queryRawUnsafe(testQuery);
    
    console.log(`   ✅ تم جلب ${maintenanceReceipts.length} سجل بنجاح`);

    // إعادة تشغيل Prisma لتحديث cache
    console.log('\n🔄 إعادة تشغيل Prisma client...');
    await prisma.$disconnect();
    
    // إنشاء instance جديد
    const newPrisma = new PrismaClient();
    
    try {
      const testWithPrisma = await newPrisma.maintenanceReceiptOrder.findMany({ take: 1 });
      console.log(`   ✅ Prisma client يعمل بشكل صحيح - جلب ${testWithPrisma.length} سجل`);
      await newPrisma.$disconnect();
    } catch (prismaError) {
      console.log(`   ⚠️  Prisma client cache قد يحتاج إعادة تشغيل الخادم: ${prismaError.message}`);
      await newPrisma.$disconnect();
    }

    console.log('\n✅ تم إكمال إصلاح الجدول بنجاح!');
    console.log('\nملاحظة: قد تحتاج لإعادة تشغيل الخادم لتحديث Prisma client cache');

  } catch (error) {
    console.error('❌ خطأ أثناء إصلاح الجدول:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// تشغيل السكريبت
if (require.main === module) {
  fixMaintenanceReceiptOrderTable()
    .then(() => {
      console.log('\n🎉 تم إنجاز إصلاح الجدول بنجاح!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 فشل إصلاح الجدول:', error);
      process.exit(1);
    });
}

module.exports = { fixMaintenanceReceiptOrderTable };
