const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function recreateEvaluationTables() {
  try {
    console.log('🗑️ Dropping evaluation tables...');

    // حذف الجداول المرتبطة بالتقييم
    await prisma.$executeRaw`DROP TABLE IF EXISTS evaluation_order_items CASCADE`;
    await prisma.$executeRaw`DROP TABLE IF EXISTS evaluation_orders CASCADE`;

    console.log('✅ Evaluation tables dropped!');

    console.log('🏗️ Recreating evaluation tables...');

    // إنشاء جدول evaluation_orders
    await prisma.$executeRaw`
      CREATE TABLE evaluation_orders (
        id SERIAL PRIMARY KEY,
        "orderId" TEXT UNIQUE NOT NULL,
        "employeeName" TEXT NOT NULL,
        date TIMESTAMP NOT NULL,
        notes TEXT,
        status TEXT NOT NULL DEFAULT 'معلق',
        "acknowledgedBy" TEXT,
        "acknowledgedDate" TIMESTAMP,
        "warehouseName" TEXT,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now()
      )
    `;

    // إنشاء جدول evaluation_order_items
    await prisma.$executeRaw`
      CREATE TABLE evaluation_order_items (
        id SERIAL PRIMARY KEY,
        "evaluationOrderId" INTEGER NOT NULL,
        "deviceId" TEXT NOT NULL,
        model TEXT NOT NULL,
        "externalGrade" TEXT NOT NULL,
        "screenGrade" TEXT NOT NULL,
        "networkGrade" TEXT NOT NULL,
        "finalGrade" TEXT NOT NULL,
        fault TEXT,
        "damageType" TEXT,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT fk_evaluation_order
          FOREIGN KEY ("evaluationOrderId") 
          REFERENCES evaluation_orders(id) 
          ON DELETE CASCADE
      )
    `;

    console.log('✅ Evaluation tables recreated with correct types!');

    // اختبار إنشاء evaluation order
    console.log('🧪 Testing evaluation order creation...');
    const testData = {
      orderId: 'TEST-001',
      employeeName: 'System Administrator',
      date: new Date(),
      status: 'معلق',
      notes: null,
      acknowledgedBy: null,
      acknowledgedDate: null,
      warehouseName: null
    };

    const testEval = await prisma.evaluationOrder.create({
      data: testData
    });

    console.log('✅ Test creation successful!', testEval.id);

    // إنشاء test item
    await prisma.evaluationOrderItem.create({
      data: {
        evaluationOrderId: testEval.id,
        deviceId: '123456789012345',
        model: 'Test Model',
        externalGrade: 'بدون',
        screenGrade: 'بدون', 
        networkGrade: 'مفتوح رسمي',
        finalGrade: 'جاهز للبيع',
        fault: null,
        damageType: null
      }
    });

    console.log('✅ Test item creation successful!');

    // تنظيف
    await prisma.evaluationOrder.delete({ where: { id: testEval.id } });

    console.log('🎉 All tests passed! Evaluation system is ready.');

  } catch (error) {
    console.error('❌ Recreation failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

recreateEvaluationTables();
