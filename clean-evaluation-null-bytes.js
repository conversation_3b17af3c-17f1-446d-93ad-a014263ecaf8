const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function cleanEvaluationNullBytes() {
  try {
    console.log('🔍 فحص جدول evaluation_orders للبحث عن null bytes...');

    // دالة تنظيف البيانات
    const sanitizeString = (str) => {
      if (!str || typeof str !== 'string') return str;
      // إزالة null bytes وأحرف التحكم الأخرى
      return str.replace(/\0/g, '')
                .replace(/[\x00-\x08]/g, '')
                .replace(/[\x0B\x0C]/g, '')
                .replace(/[\x0E-\x1F]/g, '')
                .replace(/[\x7F-\x9F]/g, '')
                .trim() || null;
    };

    // جلب جميع أوامر التقييم
    const evaluationOrders = await prisma.evaluationOrder.findMany({
      include: {
        items: true
      }
    });

    console.log(`📊 تم العثور على ${evaluationOrders.length} أمر تقييم.`);

    let cleanedOrdersCount = 0;
    let cleanedItemsCount = 0;

    for (const order of evaluationOrders) {
      let needsUpdate = false;
      const cleanedData = {};

      // فحص وتنظيف بيانات الأمر
      if (order.orderId && order.orderId !== sanitizeString(order.orderId)) {
        cleanedData.orderId = sanitizeString(order.orderId);
        needsUpdate = true;
      }

      if (order.employeeName && order.employeeName !== sanitizeString(order.employeeName)) {
        cleanedData.employeeName = sanitizeString(order.employeeName);
        needsUpdate = true;
      }

      if (order.notes && order.notes !== sanitizeString(order.notes)) {
        cleanedData.notes = sanitizeString(order.notes);
        needsUpdate = true;
      }

      if (order.warehouseName && order.warehouseName !== sanitizeString(order.warehouseName)) {
        cleanedData.warehouseName = sanitizeString(order.warehouseName);
        needsUpdate = true;
      }

      if (order.acknowledgedBy && order.acknowledgedBy !== sanitizeString(order.acknowledgedBy)) {
        cleanedData.acknowledgedBy = sanitizeString(order.acknowledgedBy);
        needsUpdate = true;
      }

      // تحديث الأمر إذا كان يحتاج تنظيف
      if (needsUpdate) {
        console.log(`🔧 تنظيف أمر التقييم: ${order.orderId}`);
        await prisma.evaluationOrder.update({
          where: { id: order.id },
          data: cleanedData
        });
        cleanedOrdersCount++;
      }

      // فحص وتنظيف عناصر الأمر
      for (const item of order.items) {
        let itemNeedsUpdate = false;
        const cleanedItemData = {};

        const fieldsToCheck = [
          'deviceId', 'model', 'externalGrade', 'screenGrade', 
          'networkGrade', 'finalGrade', 'fault', 'damageType'
        ];

        for (const field of fieldsToCheck) {
          if (item[field] && item[field] !== sanitizeString(item[field])) {
            cleanedItemData[field] = sanitizeString(item[field]);
            itemNeedsUpdate = true;
          }
        }

        if (itemNeedsUpdate) {
          console.log(`🔧 تنظيف عنصر: ${item.deviceId}`);
          await prisma.evaluationOrderItem.update({
            where: { id: item.id },
            data: cleanedItemData
          });
          cleanedItemsCount++;
        }
      }
    }

    console.log(`✅ اكتمل التنظيف:`);
    console.log(`   📋 أوامر تم تنظيفها: ${cleanedOrdersCount}`);
    console.log(`   📱 عناصر تم تنظيفها: ${cleanedItemsCount}`);

    if (cleanedOrdersCount === 0 && cleanedItemsCount === 0) {
      console.log('🎉 لم يتم العثور على أي null bytes في البيانات!');
    }

  } catch (error) {
    console.error('❌ خطأ أثناء تنظيف البيانات:', error);
  } finally {
    await prisma.$disconnect();
  }
}

cleanEvaluationNullBytes();
