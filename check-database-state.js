const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkDatabaseState() {
  try {
    console.log('🔍 Checking database state...');

    // فحص الاتصال الأساسي
    console.log('\n1️⃣ Testing basic connection:');
    const result = await prisma.$queryRaw`SELECT 1 as test`;
    console.log('✅ Basic connection OK:', result);

    // فحص encoding قاعدة البيانات
    console.log('\n2️⃣ Checking database encoding:');
    const encoding = await prisma.$queryRaw`SHOW server_encoding`;
    console.log('Database encoding:', encoding);

    // فحص client encoding
    const clientEncoding = await prisma.$queryRaw`SHOW client_encoding`;
    console.log('Client encoding:', clientEncoding);

    // فحص جدول evaluation_orders
    console.log('\n3️⃣ Checking evaluation_orders table structure:');
    const tableInfo = await prisma.$queryRaw`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'evaluation_orders'
      ORDER BY ordinal_position
    `;
    console.log('Table structure:', tableInfo);

    // فحص البيانات الموجودة
    console.log('\n4️⃣ Checking existing data:');
    const existingData = await prisma.$queryRaw`
      SELECT id, "orderId", "employeeName", status, date 
      FROM evaluation_orders 
      LIMIT 5
    `;
    console.log('Existing records:', existingData);

    // محاولة إنشاء جدول test بسيط
    console.log('\n5️⃣ Testing simple table creation:');
    await prisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS test_encoding (
        id SERIAL PRIMARY KEY,
        test_text TEXT
      )
    `;
    
    await prisma.$executeRaw`
      INSERT INTO test_encoding (test_text) VALUES ('simple test')
    `;
    
    const testResult = await prisma.$queryRaw`SELECT * FROM test_encoding`;
    console.log('✅ Simple test table works:', testResult);

    // تنظيف
    await prisma.$executeRaw`DROP TABLE IF EXISTS test_encoding`;

  } catch (error) {
    console.error('❌ Database check failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkDatabaseState();
