import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q') || '';
    const limit = parseInt(searchParams.get('limit') || '5');
    
    if (query.length < 2) {
      return NextResponse.json([]);
    }
    
    const results = await prisma.employeeRequest.findMany({
      where: {
        isArchived: false,
        OR: [
          { requestNumber: { contains: query, mode: 'insensitive' } },
          { employeeName: { contains: query, mode: 'insensitive' } }
        ]
      },
      select: {
        id: true,
        requestNumber: true,
        employeeName: true,
        requestType: true,
        priority: true,
        status: true,
        requestDate: true
      },
      orderBy: { requestDate: 'desc' },
      take: limit
    });

    const formattedResults = results.map(request => ({
      id: request.id,
      requestNumber: request.requestNumber,
      employeeName: request.employeeName,
      requestType: request.requestType,
      priority: request.priority,
      status: request.status,
      requestDate: request.requestDate.toISOString(),
      notes: ''
    }));

    return NextResponse.json(formattedResults);
  } catch (error) {
    console.error('خطأ في البحث السريع:', error);
    return NextResponse.json({ error: 'Quick search failed' }, { status: 500 });
  }
}
