const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testAllReturnOperations() {
  try {
    console.log('🧪 اختبار جميع عمليات المرتجعات...');
    
    // 1. اختبار إنشاء مرتجع
    console.log('\n1. اختبار إنشاء مرتجع...');
    
    const device = await prisma.device.findFirst({
      where: { status: 'متاح للبيع' }
    });

    if (!device) {
      console.log('❌ لا توجد أجهزة متاحة للاختبار');
      return;
    }

    const roNumber = `RO-TEST-${Date.now()}`;
    
    // إنشاء مرتجع مباشرة بـ raw SQL
    const createResult = await prisma.$queryRaw`
      INSERT INTO "Return" 
      ("roNumber", "opReturnNumber", "date", "saleId", "soNumber", "clientName", "warehouseName", "notes", "status", "processedBy", "processedDate", "employeeName", "attachments")
      VALUES 
      (${roNumber}, ${roNumber}, ${new Date().toISOString()}, ${1}, ${'SO-TEST'}, ${'عميل تجريبي'}, ${'مخزن تجريبي'}, ${'مرتجع تجريبي'}, ${'معلق'}, ${null}, ${null}, ${'مختبر النظام'}, ${null})
      RETURNING *
    `.then(result => Array.isArray(result) ? result[0] : result);

    console.log('✅ تم إنشاء المرتجع:', createResult.roNumber);

    // إضافة عنصر للمرتجع
    await prisma.$executeRaw`
      INSERT INTO "return_items" 
      ("returnId", "deviceId", "model", "returnReason", "isReplacement")
      VALUES 
      (${createResult.id}, ${device.id}, ${device.model || 'غير محدد'}, ${'عيب في الجهاز'}, ${false})
    `;

    console.log('✅ تم إضافة عنصر للمرتجع');

    // 2. اختبار قراءة المرتجع
    console.log('\n2. اختبار قراءة المرتجع...');
    
    const readResult = await prisma.$queryRaw`
      SELECT 
        r.id, r."roNumber", r."opReturnNumber", r.date, r."saleId", r."soNumber", 
        r."clientName", r."warehouseName", r.notes, r.status, r."processedBy", 
        r."processedDate", r."employeeName", r."createdAt", r.attachments,
        json_agg(
          json_build_object(
            'id', ri.id,
            'deviceId', ri."deviceId", 
            'model', ri.model,
            'returnReason', ri."returnReason",
            'isReplacement', ri."isReplacement"
          )
        ) FILTER (WHERE ri.id IS NOT NULL) as items
      FROM "Return" r
      LEFT JOIN "return_items" ri ON r.id = ri."returnId"
      WHERE r.id = ${createResult.id}
      GROUP BY r.id, r."roNumber", r."opReturnNumber", r.date, r."saleId", r."soNumber", 
               r."clientName", r."warehouseName", r.notes, r.status, r."processedBy", 
               r."processedDate", r."employeeName", r."createdAt", r.attachments
    `;

    const returnData = Array.isArray(readResult) ? readResult[0] : readResult;
    console.log('✅ تم قراءة المرتجع:', returnData.roNumber);
    console.log('   - عدد العناصر:', returnData.items ? returnData.items.length : 0);

    // 3. اختبار تحديث المرتجع
    console.log('\n3. اختبار تحديث المرتجع...');
    
    await prisma.$executeRaw`
      UPDATE "Return" 
      SET 
        "notes" = ${'تم التحديث - اختبار'},
        "status" = ${'مكتمل'}
      WHERE id = ${createResult.id}
    `;

    console.log('✅ تم تحديث المرتجع');

    // 4. اختبار حذف المرتجع
    console.log('\n4. اختبار حذف المرتجع...');
    
    // حذف العناصر أولاً
    await prisma.$executeRaw`
      DELETE FROM "return_items" WHERE "returnId" = ${createResult.id}
    `;

    // حذف المرتجع
    await prisma.$executeRaw`
      DELETE FROM "Return" WHERE id = ${createResult.id}
    `;

    console.log('✅ تم حذف المرتجع');

    // التحقق من الحذف
    const deletedCheck = await prisma.$queryRaw`
      SELECT COUNT(*) as count FROM "Return" WHERE id = ${createResult.id}
    `;

    const count = parseInt(deletedCheck[0].count);
    if (count === 0) {
      console.log('✅ تم التأكد من الحذف بنجاح');
    } else {
      console.log('❌ فشل في الحذف');
    }

    console.log('\n🎉 جميع الاختبارات نجحت!');

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testAllReturnOperations();
