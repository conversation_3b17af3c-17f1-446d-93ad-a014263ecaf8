"use client";

import { jsPDF } from "jspdf";
import { formatDate, formatTime } from '@/lib/date-utils';

// الألوان المطابقة لتصميم HTML المحسن
const COLORS = {
  primary: '#4299e1',      // أزرق أساسي
  secondary: '#2b6cb0',    // أزرق غامق
  text: {
    primary: '#1a202c',    // نص أساسي
    secondary: '#4a5568',  // نص ثانوي
    muted: '#718096'       // نص خافت
  },
  background: {
    main: '#ffffff',       // خلفية رئيسية
    card: '#f7fafc',       // خلفية البطاقات
    accent: '#edf2f7'      // خلفية مميزة
  },
  borders: {
    light: '#e2e8f0',      // حدود خفيفة
    medium: '#cbd5e0',     // حدود متوسطة
    dark: '#a0aec0'        // حدود غامقة
  }
};

// الخطوط والأحجام المطابقة لـ HTML
const FONTS = {
  sizes: {
    h1: 28,
    h2: 24, 
    h3: 20,
    h4: 18,
    body: 14,
    small: 12,
    tiny: 10
  },
  families: [
    'Cairo',
    'Noto Sans Arabic', 
    'Tajawal',
    'Arial',
    'sans-serif'
  ]
};

/**
 * وظيفة محسنة لإنشاء PDF باستخدام Canvas مع تصميم مطابق لـ HTML
 */
export async function createArabicPDFWithCanvas(
  deviceInfo: any,
  timelineEvents: any[],
  fileName: string,
  isCustomerView: boolean = false,
  action: 'print' | 'download' = 'download',
  language: 'ar' | 'en' | 'both' = 'both'
): Promise<void> {
  try {
    // جلب الإعدادات أولاً
    const settings = await fetchSystemSettings();
    
    // إنشاء PDF
    const pdf = new jsPDF('p', 'mm', 'a4');
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();

    // إنشاء Canvas مؤقت لرسم النصوص
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) throw new Error('فشل في إنشاء Canvas context');

    // إعداد Canvas بدقة عالية
    const scale = 2; // تحسين الدقة
    canvas.width = 794 * scale;
    canvas.height = 1123 * scale;
    ctx.scale(scale, scale);
    
    // إعداد Canvas الأساسي
    setupCanvasDefaults(ctx, canvas);
    
    let currentY = 20;
    let pages = [];

    // رسم الصفحة الأولى
    drawBackground(ctx, canvas);
    currentY = await drawEnhancedHeader(ctx, canvas, settings, language, currentY);
    currentY = drawReportTitle(ctx, canvas, deviceInfo, isCustomerView, language, currentY);
    currentY = await drawDeviceInfo(ctx, canvas, deviceInfo, language, currentY);
    
    // رسم معلومات البيع والضمان للعميل
    if (isCustomerView) {
      if (deviceInfo.lastSale) {
        currentY = await drawSaleInfo(ctx, canvas, deviceInfo.lastSale, language, currentY);
      }
      if (deviceInfo.warrantyInfo) {
        currentY = await drawWarrantyInfo(ctx, canvas, deviceInfo.warrantyInfo, language, currentY);
      }
    }
    
    // رسم سجل الأحداث
    if (timelineEvents && timelineEvents.length > 0) {
      // تصفية الأحداث للعملاء
      const filteredEvents = isCustomerView 
        ? timelineEvents.filter(event => !event.type?.includes('توريد') && !event.type?.includes('تقييم'))
        : timelineEvents;
        
      if (filteredEvents.length > 0) {
        currentY = await drawTimelineEvents(ctx, canvas, pdf, filteredEvents, language, currentY, pageHeight);
      }
    }
    
    // رسم التذييل
    drawEnhancedFooter(ctx, canvas, settings, language);

    // إضافة الصفحة إلى PDF
    const imgData = canvas.toDataURL('image/png', 1.0);
    pdf.addImage(imgData, 'PNG', 0, 0, pageWidth, pageHeight);
    
    // حفظ أو طباعة الملف
    if (action === 'print') {
      pdf.output('dataurlnewwindow');
    } else {
      pdf.save(`${fileName}.pdf`);
    }

    // تنظيف
    canvas.remove();

    // عرض رسالة نجاح
    showNotification('تم إنشاء التقرير بنجاح باستخدام Canvas المحسن!', 'success');
    
  } catch (error) {
    console.error('Error creating enhanced PDF with Canvas:', error);
    showNotification('حدث خطأ أثناء التصدير: ' + (error as Error).message, 'error');
  }
}
/**
 * جلب إعدادات النظام
 */
async function fetchSystemSettings(): Promise<any> {
  try {
    const response = await fetch('/api/settings');
    if (response.ok) {
      return await response.json();
    }
  } catch (error) {
    console.warn('فشل في جلب الإعدادات، سيتم استخدام القيم الافتراضية');
  }
  
  return {
    companyNameAr: 'DeviceFlow',
    companyNameEn: 'DeviceFlow',
    addressAr: 'الشارع الرئيسي، المدينة، الدولة',
    addressEn: 'Main Street, City, Country',
    phone: '+************',
    email: '<EMAIL>',
    website: 'www.deviceflow.com',
    footerTextAr: 'شكرًا لتعاملكم معنا.',
    footerTextEn: 'Thank you for your business.'
  };
}

/**
 * إعداد Canvas الأساسي
 */
function setupCanvasDefaults(ctx: CanvasRenderingContext2D, canvas: HTMLCanvasElement): void {
  // خلفية بيضاء
  ctx.fillStyle = COLORS.background.main;
  ctx.fillRect(0, 0, canvas.width, canvas.height);
  
  // إعدادات النص المحسنة
  ctx.textRenderingOptimization = 'optimizeLegibility';
  ctx.imageSmoothingEnabled = true;
  ctx.imageSmoothingQuality = 'high';
  
  // إعدادات الاتجاه للعربية
  ctx.direction = 'rtl';
  ctx.textAlign = 'right';
}

/**
 * رسم خلفية تدرجية مطابقة لـ HTML
 */
function drawBackground(ctx: CanvasRenderingContext2D, canvas: HTMLCanvasElement): void {
  // تدرج خفيف في الخلفية
  const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
  gradient.addColorStop(0, COLORS.background.main);
  gradient.addColorStop(0.1, COLORS.background.accent);
  gradient.addColorStop(1, COLORS.background.main);
  
  ctx.fillStyle = gradient;
  ctx.fillRect(0, 0, canvas.width, canvas.height);
}

/**
 * رسم ترويسة محسنة مطابقة لتصميم HTML
 */
async function drawEnhancedHeader(
  ctx: CanvasRenderingContext2D, 
  canvas: HTMLCanvasElement, 
  settings: any, 
  language: string, 
  startY: number
): Promise<number> {
  let currentY = startY;
  const centerX = canvas.width / 2;
  const padding = 30;
  
  // رسم خلفية الترويسة مع تدرج
  const headerHeight = 120;
  const headerGradient = ctx.createLinearGradient(0, currentY, 0, currentY + headerHeight);
  headerGradient.addColorStop(0, COLORS.background.accent);
  headerGradient.addColorStop(1, COLORS.background.card);
  
  ctx.fillStyle = headerGradient;
  ctx.fillRect(padding, currentY, canvas.width - (padding * 2), headerHeight);
  
  // رسم حدود الترويسة
  ctx.strokeStyle = COLORS.primary;
  ctx.lineWidth = 3;
  ctx.strokeRect(padding, currentY, canvas.width - (padding * 2), headerHeight);
  
  currentY += 25;
  
  // رسم أسماء الشركة
  if (language === 'ar' || language === 'both') {
    ctx.font = `bold ${FONTS.sizes.h2}px ${FONTS.families[0]}, Arial, sans-serif`;
    ctx.fillStyle = COLORS.secondary;
    ctx.textAlign = 'center';
    ctx.fillText(settings.companyNameAr || 'DeviceFlow', centerX, currentY);
    currentY += 30;
  }

  if (language === 'en' || language === 'both') {
    const fontSize = language === 'both' ? FONTS.sizes.body + 2 : FONTS.sizes.h2;
    ctx.font = `${language === 'both' ? 'normal' : 'bold'} ${fontSize}px Arial, sans-serif`;
    ctx.fillStyle = language === 'both' ? COLORS.text.secondary : COLORS.secondary;
    ctx.textAlign = 'center';
    ctx.fillText(settings.companyNameEn || 'DeviceFlow', centerX, currentY);
    currentY += language === 'both' ? 25 : 30;
  }

  // رسم العناوين والمعلومات
  ctx.font = `${FONTS.sizes.small}px Arial, sans-serif`;
  ctx.fillStyle = COLORS.text.muted;
  ctx.textAlign = 'center';

  if (language === 'ar' || language === 'both') {
    ctx.fillText(settings.addressAr || 'الشارع الرئيسي، المدينة، الدولة', centerX, currentY);
    currentY += 16;
  }

  if (language === 'en' || language === 'both') {
    ctx.fillText(settings.addressEn || 'Main Street, City, Country', centerX, currentY);
    currentY += 16;
  }

  // رسم معلومات الاتصال
  const contactInfo = [];
  if (settings.phone) contactInfo.push(`📞 ${settings.phone}`);
  if (settings.email) contactInfo.push(`✉️ ${settings.email}`);
  if (settings.website) contactInfo.push(`🌐 ${settings.website}`);

  if (contactInfo.length > 0) {
    ctx.font = `${FONTS.sizes.tiny}px Arial, sans-serif`;
    ctx.fillText(contactInfo.join(' | '), centerX, currentY);
    currentY += 15;
  }

  return currentY + 20;
}

/**
 * رسم عنوان التقرير
 */
function drawReportTitle(
  ctx: CanvasRenderingContext2D,
  canvas: HTMLCanvasElement,
  deviceInfo: any,
  isCustomerView: boolean,
  language: string,
  startY: number
): number {
  let currentY = startY;
  const centerX = canvas.width / 2;
  
  // تحديد عنوان التقرير
  const reportTitle = isCustomerView
    ? (language === 'en' ? 'Device Tracking Report (Customer Copy)' : 'تقرير تتبع الجهاز (نسخة العميل)')
    : (language === 'en' ? 'Device History Log' : 'سجل تاريخ الجهاز');

  // رسم العنوان الرئيسي
  ctx.font = `bold ${FONTS.sizes.h1}px ${FONTS.families[0]}, Arial, sans-serif`;
  ctx.fillStyle = COLORS.text.primary;
  ctx.textAlign = 'center';
  ctx.fillText(reportTitle, centerX, currentY);
  currentY += 35;

  // رسم العنوان الفرعي
  ctx.font = `${FONTS.sizes.body + 2}px Arial, sans-serif`;
  ctx.fillStyle = COLORS.text.secondary;
  ctx.fillText(`${deviceInfo.model} - ${deviceInfo.id}`, centerX, currentY);
  currentY += 25;

  // رسم الطابع الزمني
  const currentDate = new Date();
  const arabicDate = formatDate(currentDate, { arabic: true });
  const englishDate = formatDate(currentDate);
  const time = formatTime(currentDate);

  ctx.font = `${FONTS.sizes.tiny}px Arial, sans-serif`;
  ctx.fillStyle = COLORS.borders.dark;

  let timestampText = '';
  if (language === 'ar' || language === 'both') {
    timestampText += `تاريخ الطباعة: ${arabicDate} - ${time}`;
  }
  if (language === 'both') {
    timestampText += ' | ';
  }
  if (language === 'en' || language === 'both') {
    timestampText += `Print Date: ${englishDate} - ${time}`;
  }
  
  ctx.fillText(timestampText, centerX, currentY);
  
  return currentY + 30;
}

/**
 * رسم معلومات الجهاز في بطاقة محسنة
 */
async function drawDeviceInfo(
  ctx: CanvasRenderingContext2D,
  canvas: HTMLCanvasElement,
  deviceInfo: any,
  language: string,
  startY: number
): Promise<number> {
  let currentY = startY;
  const padding = 50;
  const cardWidth = canvas.width - (padding * 2);
  const cardHeight = 120;
  
  // رسم بطاقة المعلومات
  drawInfoCard(ctx, padding, currentY, cardWidth, cardHeight, 
    language === 'en' ? 'Device Information' : 'معلومات الجهاز');
  
  currentY += 55; // بعد عنوان البطاقة
  
  // رسم المعلومات في شبكة
  const itemHeight = 25;
  const rightCol = canvas.width - 70;
  const leftCol = canvas.width - 350;
  
  ctx.font = `${FONTS.sizes.small}px ${FONTS.families[0]}, Arial, sans-serif`;
  
  // الموديل
  drawInfoItem(ctx, rightCol, currentY, 
    language === 'en' ? 'Model:' : 'الموديل:', 
    deviceInfo.model || '-');
  
  // الرقم التسلسلي  
  drawInfoItem(ctx, leftCol, currentY,
    language === 'en' ? 'Serial Number:' : 'الرقم التسلسلي:',
    deviceInfo.id || '-');
  
  currentY += itemHeight;
  
  // الحالة
  drawInfoItem(ctx, rightCol, currentY,
    language === 'en' ? 'Status:' : 'الحالة:',
    deviceInfo.status || '-');
  
  return currentY + 40;
}

/**
 * رسم معلومات البيع
 */
async function drawSaleInfo(
  ctx: CanvasRenderingContext2D,
  canvas: HTMLCanvasElement,
  lastSale: any,
  language: string,
  startY: number
): Promise<number> {
  let currentY = startY;
  const padding = 50;
  const cardWidth = canvas.width - (padding * 2);
  const cardHeight = 140;
  
  // رسم بطاقة معلومات البيع
  drawInfoCard(ctx, padding, currentY, cardWidth, cardHeight,
    language === 'en' ? 'Sale Information' : 'تفاصيل البيع', COLORS.primary);
  
  currentY += 55;
  
  const itemHeight = 25;
  const rightCol = canvas.width - 70;
  const leftCol = canvas.width - 350;
  
  ctx.font = `${FONTS.sizes.small}px ${FONTS.families[0]}, Arial, sans-serif`;
  
  // العميل
  drawInfoItem(ctx, rightCol, currentY,
    language === 'en' ? 'Customer:' : 'العميل:',
    lastSale.clientName || '-');
    
  // فاتورة البيع
  drawInfoItem(ctx, leftCol, currentY,
    language === 'en' ? 'Sales Invoice:' : 'فاتورة البيع:',
    lastSale.soNumber || '-');
  
  currentY += itemHeight;
  
  // الفاتورة الرسمية
  drawInfoItem(ctx, rightCol, currentY,
    language === 'en' ? 'Official Invoice:' : 'الفاتورة الرسمية:',
    lastSale.opNumber || 'لا يوجد');
    
  // تاريخ البيع
  const saleDate = formatDate(lastSale.date, { arabic: true });
  drawInfoItem(ctx, leftCol, currentY,
    language === 'en' ? 'Sale Date:' : 'تاريخ البيع:',
    saleDate);
  
  return currentY + 40;
}

/**
 * رسم معلومات الضمان
 */
async function drawWarrantyInfo(
  ctx: CanvasRenderingContext2D,
  canvas: HTMLCanvasElement,
  warrantyInfo: any,
  language: string,
  startY: number
): Promise<number> {
  let currentY = startY;
  const padding = 50;
  const cardWidth = canvas.width - (padding * 2);
  const cardHeight = 120;
  
  // تحديد لون البطاقة حسب حالة الضمان
  let cardColor = COLORS.borders.medium;
  if (warrantyInfo.status?.includes('ساري')) {
    cardColor = '#10B981'; // أخضر للضمان الساري
  } else if (warrantyInfo.status?.includes('منتهي')) {
    cardColor = '#EF4444'; // أحمر للضمان المنتهي
  }
  
  // رسم بطاقة معلومات الضمان
  drawInfoCard(ctx, padding, currentY, cardWidth, cardHeight,
    language === 'en' ? 'Warranty Information' : 'حالة الضمان', cardColor);
  
  currentY += 55;
  
  const itemHeight = 25;
  const rightCol = canvas.width - 70;
  const leftCol = canvas.width - 350;
  
  ctx.font = `${FONTS.sizes.small}px ${FONTS.families[0]}, Arial, sans-serif`;
  
  // حالة الضمان
  drawInfoItem(ctx, rightCol, currentY,
    language === 'en' ? 'Status:' : 'الحالة:',
    warrantyInfo.status || '-');
    
  // تاريخ الانتهاء
  drawInfoItem(ctx, leftCol, currentY,
    language === 'en' ? 'Expiry Date:' : 'تاريخ الانتهاء:',
    warrantyInfo.expiryDate || '-');
  
  currentY += itemHeight;
  
  // الوقت المتبقي
  drawInfoItem(ctx, rightCol, currentY,
    language === 'en' ? 'Remaining Time:' : 'الوقت المتبقي:',
    warrantyInfo.remaining || '-');
  
  return currentY + 40;
}

// وظيفة مساعدة لعرض الإشعارات (نسخة من الملف الآخر)
function showNotification(message: string, type: 'success' | 'error' = 'success') {
  const notification = document.createElement("div");
  notification.textContent = message;
  notification.style.position = "fixed";
  notification.style.top = "20px";
  notification.style.right = "20px";
  notification.style.padding = "12px 20px";
  notification.style.background = type === 'success' ? "#10B981" : "#EF4444";
  notification.style.color = "white";
  notification.style.borderRadius = "8px";
  notification.style.zIndex = "9999";
  notification.style.boxShadow = "0 4px 12px rgba(0, 0, 0, 0.15)";
  notification.style.fontSize = "14px";
  notification.style.fontWeight = "500";
  notification.setAttribute('data-notification-id', 'canvas-pdf-notification');
  
  document.body.appendChild(notification);
  
  setTimeout(() => {
    if (document.body.contains(notification)) {
      document.body.removeChild(notification);
    }
  }, 3000);
}

// دالة رسم الترويسة مع الإعدادات الجديدة
async function drawHeader(
  ctx: CanvasRenderingContext2D,
  canvas: HTMLCanvasElement,
  settings: any,
  startY: number,
  language: string
): Promise<number> {
  let currentY = startY;

  // رسم خط فاصل علوي
  ctx.strokeStyle = '#3498db';
  ctx.lineWidth = 3;
  ctx.beginPath();
  ctx.moveTo(50, currentY);
  ctx.lineTo(canvas.width - 50, currentY);
  ctx.stroke();
  currentY += 20;

  // رسم أسماء الشركة
  if (language === 'ar' || language === 'both') {
    ctx.font = 'bold 20px Arial, sans-serif';
    ctx.fillStyle = '#2c3e50';
    ctx.textAlign = 'center';
    ctx.fillText(settings.companyNameAr || 'DeviceFlow', canvas.width / 2, currentY);
    currentY += 25;
  }

  if (language === 'en' || language === 'both') {
    ctx.font = language === 'both' ? '16px Arial, sans-serif' : 'bold 20px Arial, sans-serif';
    ctx.fillStyle = language === 'both' ? '#7f8c8d' : '#2c3e50';
    ctx.textAlign = 'center';
    ctx.fillText(settings.companyNameEn || 'DeviceFlow', canvas.width / 2, currentY);
    currentY += language === 'both' ? 20 : 25;
  }

  // رسم العناوين
  ctx.font = '14px Arial, sans-serif';
  ctx.fillStyle = '#7f8c8d';
  ctx.textAlign = 'center';

  if (language === 'ar' || language === 'both') {
    ctx.fillText(settings.addressAr || 'الشارع الرئيسي، المدينة، الدولة', canvas.width / 2, currentY);
    currentY += 18;
  }

  if (language === 'en' || language === 'both') {
    ctx.fillText(settings.addressEn || 'Main Street, City, Country', canvas.width / 2, currentY);
    currentY += 18;
  }

  // رسم معلومات الاتصال
  const contactInfo = [];
  if (settings.phone) contactInfo.push(`📞 ${settings.phone}`);
  if (settings.email) contactInfo.push(`✉️ ${settings.email}`);
  if (settings.website) contactInfo.push(`🌐 ${settings.website}`);

  if (contactInfo.length > 0) {
    ctx.fillText(contactInfo.join(' | '), canvas.width / 2, currentY);
    currentY += 20;
  }

  // رسم خط فاصل سفلي
  ctx.strokeStyle = '#bdc3c7';
  ctx.lineWidth = 1;
  ctx.beginPath();
  ctx.moveTo(50, currentY);
  ctx.lineTo(canvas.width - 50, currentY);
  ctx.stroke();
  currentY += 30;

  return currentY;
}

// دالة رسم التذييل
function drawFooter(
  ctx: CanvasRenderingContext2D,
  canvas: HTMLCanvasElement,
  settings: any,
  language: string
): void {
  const footerY = canvas.height - 80;

  // رسم خط فاصل
  ctx.strokeStyle = '#bdc3c7';
  ctx.lineWidth = 1;
  ctx.beginPath();
  ctx.moveTo(50, footerY);
  ctx.lineTo(canvas.width - 50, footerY);
  ctx.stroke();

  // رسم نصوص التذييل
  ctx.font = '12px Arial, sans-serif';
  ctx.fillStyle = '#7f8c8d';
  ctx.textAlign = 'center';

  let currentY = footerY + 20;

  if (language === 'ar' || language === 'both') {
    if (settings.footerTextAr) {
      ctx.fillText(settings.footerTextAr, canvas.width / 2, currentY);
      currentY += 15;
    }
  }

  if (language === 'en' || language === 'both') {
    if (settings.footerTextEn) {
      ctx.fillText(settings.footerTextEn, canvas.width / 2, currentY);
      currentY += 15;
    }
  }

  // رسم الطابع الزمني
  const currentDate = new Date();
  const arabicDate = formatDate(currentDate, { arabic: true });
  const englishDate = formatDate(currentDate);
  const time = formatTime(currentDate);

  ctx.font = '10px Arial, sans-serif';
  ctx.fillStyle = '#95a5a6';

  if (language === 'ar' || language === 'both') {
    const timestampAr = `تاريخ الطباعة: ${arabicDate} - ${time}`;
    ctx.textAlign = language === 'both' ? 'right' : 'center';
    const xPos = language === 'both' ? canvas.width - 60 : canvas.width / 2;
    ctx.fillText(timestampAr, xPos, currentY);
  }

  if (language === 'en' || language === 'both') {
    const timestampEn = `Print Date: ${englishDate} - ${time}`;
    ctx.textAlign = language === 'both' ? 'left' : 'center';
    const xPos = language === 'both' ? 60 : canvas.width / 2;
    ctx.fillText(timestampEn, xPos, currentY);
  }
}
