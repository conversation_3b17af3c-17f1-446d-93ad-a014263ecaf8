"use client";

import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { formatDate, formatTime, formatDateTime } from '@/lib/date-utils';
import "jspdf-autotable";

// تصدير إلى PDF عن طريق تحويل HTML
export function exportHTMLToPDF(
  elementId: string,
  fileName: string,
  title: string = "تقرير"
) {
  try {
    const notification = document.createElement("div");
    notification.textContent = "جاري تحضير ملف PDF...";
    notification.style.position = "fixed";
    notification.style.top = "20px";
    notification.style.right = "20px";
    notification.style.padding = "10px 20px";
    notification.style.background = "#4CAF50";
    notification.style.color = "white";
    notification.style.borderRadius = "5px";
    notification.style.zIndex = "9999";
    notification.setAttribute('data-notification-id', 'html-pdf-notification');
    document.body.appendChild(notification);
    
    const element = document.getElementById(elementId);
    if (!element) {
      throw new Error(`العنصر المطلوب طباعته (${elementId}) غير موجود`);
    }
    
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      throw new Error('فشل في فتح نافذة الطباعة. يرجى التأكد من عدم حظر النوافذ المنبثقة.');
    }
    
    const content = element.innerHTML;
    
    printWindow.document.write(`
      <!DOCTYPE html>
      <html dir="rtl">
        <head>
          <meta charset="utf-8" />
          <title>${title}</title>
          <style>
            @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap');
            body {
              direction: rtl;
              font-family: 'Cairo', Arial, sans-serif;
              padding: 20px;
              line-height: 1.6;
            }
            h1 { text-align: center; margin-bottom: 20px; }
            table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
            th { background-color: #f2f2f2; }
            .no-print { display: none !important; }
            .print-section { margin-bottom: 20px; page-break-inside: avoid; }
            @media print {
              body { print-color-adjust: exact; -webkit-print-color-adjust: exact; }
              @page { size: A4; margin: 15mm; }
            }
          </style>
        </head>
        <body>
          <h1>${title}</h1>
          <div class="print-content">${content}</div>
          <script>
            document.fonts.ready.then(() => {
              setTimeout(() => {
                window.print();
                window.onafterprint = () => window.close();
              }, 1000);
            });
          </script>
        </body>
      </html>
    `);
    
    printWindow.document.close();
    
    setTimeout(() => {
      const existingNotification = document.querySelector('[data-notification-id="html-pdf-notification"]');
      if (existingNotification) {
        document.body.removeChild(existingNotification);
      }
    }, 1000);
    
  } catch (error) {
    console.error('Error exporting HTML to PDF:', error);
    alert('حدث خطأ أثناء التصدير: ' + (error as Error).message);
    
    const existingNotification = document.querySelector('[data-notification-id="html-pdf-notification"]');
    if (existingNotification) {
      document.body.removeChild(existingNotification);
    }
  }
}

// تصدير بيانات إلى PDF باستخدام جدول HTML مع دعم محسن للعربية
export function exportDataToPDF(
  data: (string | number)[][],
  headers: string[],
  fileName: string,
  title: string = "تقرير"
) {
  try {
    const doc = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });
    
    doc.setR2L(true);
    doc.setFont('helvetica');
    doc.setFontSize(18);
    
    const pageWidth = doc.internal.pageSize.getWidth();
    doc.text(title, pageWidth / 2, 20, { align: 'center' });
    
    const now = new Date();
    const dateStr = formatDate(now, { arabic: true });
    const timeStr = formatTime(now);
    doc.setFontSize(10);
    doc.text(`تاريخ التصدير: ${dateStr} - ${timeStr}`, pageWidth - 10, 10, { align: 'right' });
    
    const tableData = Array.isArray(data[0]) ? data : data.map(row => Object.values(row));
    
    (doc as any).autoTable({
        head: [headers],
        body: tableData,
        startY: 35,
        styles: {
            fontSize: 10,
            cellPadding: 3,
            halign: 'right',
            valign: 'middle',
            lineColor: [200, 200, 200],
            lineWidth: 0.1,
        },
        headStyles: {
            fillColor: [52, 73, 94],
            textColor: 255,
            fontStyle: 'bold',
            halign: 'center',
            fontSize: 11,
        },
        alternateRowStyles: {
            fillColor: [248, 249, 250],
        },
        columnStyles: {
            0: { cellWidth: 'auto' },
        },
        margin: { top: 40, right: 10, bottom: 20, left: 10 },
        tableWidth: 'auto',
        didDrawPage: function (data: any) {
            const pageNumber = data.pageNumber;
            const totalPages = (doc as any).internal.getNumberOfPages();
            doc.setFontSize(8);
            doc.text(
              `صفحة ${pageNumber} من ${totalPages}`,
              pageWidth / 2,
              doc.internal.pageSize.getHeight() - 10,
              { align: 'center' }
            );
        }
    });

    doc.save(`${fileName}.pdf`);
    showNotification('تم تصدير الملف بنجاح!', 'success');
    
  } catch (error) {
     console.error('Error exporting data to PDF:', error);
     showNotification('حدث خطأ أثناء التصدير: ' + (error as Error).message, 'error');
  }
}

// تصدير البيانات إلى CSV مع دعم اللغة العربية
export function exportToCSV(data: any[], headers: string[], fileName: string) {
  const BOM = "\uFEFF";
  const headerRow = headers.join(",");
  const rows = data.map(row => 
    Object.values(row)
      .map(value => `"${String(value || "").replace(/"/g, '""')}"`)
      .join(",")
  );
  
  let csvContent = BOM + headerRow + "\n" + rows.join("\n");
  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8" });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement("a");
  link.setAttribute("href", url);
  link.setAttribute("download", `${fileName}.csv`);
  link.style.display = "none";
  document.body.appendChild(link);
  link.click();
  
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}

// وظيفة مساعدة لعرض الإشعارات
function showNotification(message: string, type: 'success' | 'error' = 'success') {
  const notification = document.createElement("div");
  notification.textContent = message;
  notification.style.position = "fixed";
  notification.style.top = "20px";
  notification.style.right = "20px";
  notification.style.padding = "12px 20px";
  notification.style.background = type === 'success' ? "#10B981" : "#EF4444";
  notification.style.color = "white";
  notification.style.borderRadius = "8px";
  notification.style.zIndex = "9999";
  notification.style.boxShadow = "0 4px 12px rgba(0, 0, 0, 0.15)";
  notification.style.fontSize = "14px";
  notification.style.fontWeight = "500";
  notification.setAttribute('data-notification-id', 'pdf-export-notification');
  
  document.body.appendChild(notification);
  
  setTimeout(() => {
    if (document.body.contains(notification)) {
      document.body.removeChild(notification);
    }
  }, 3000);
}

// وظيفة محسنة للطباعة المباشرة
export function printElement(elementId: string, title: string = "تقرير") {
  try {
    // انتظار قصير للتأكد من تحميل العنصر
    setTimeout(() => {
      const element = document.getElementById(elementId);
      if (!element) {
        showNotification(`العنصر المطلوب طباعته (${elementId}) غير موجود. يرجى التأكد من تحميل البيانات أولاً.`, 'error');
        return;
      }

      // التحقق من أن العنصر يحتوي على محتوى
      if (!element.innerHTML.trim()) {
        showNotification('العنصر المطلوب طباعته فارغ. يرجى التأكد من وجود بيانات للطباعة.', 'error');
        return;
      }

      performPrint(element, title, elementId);
    }, 100);

  } catch (error) {
    console.error('Error printing element:', error);
    showNotification('حدث خطأ أثناء الطباعة: ' + (error as Error).message, 'error');
  }
}

// وظيفة منفصلة لتنفيذ الطباعة
function performPrint(element: HTMLElement, title: string, elementId: string) {
  try {

    // إنشاء نافذة طباعة محسنة
    const printWindow = window.open('', '_blank', 'width=800,height=600');
    if (!printWindow) {
      showNotification('فشل في فتح نافذة الطباعة. يرجى التأكد من عدم حظر النوافذ المنبثقة.', 'error');
      return;
    }

    // نسخ المحتوى مع تحسينات للطباعة
    const content = element.cloneNode(true) as HTMLElement;

    // إزالة العناصر غير المطلوبة للطباعة
    content.querySelectorAll('.no-print, button, .btn').forEach(el => el.remove());

    printWindow.document.write(`
      <!DOCTYPE html>
      <html dir="rtl">
        <head>
          <meta charset="utf-8" />
          <title>${title}</title>
          <style>
            @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap');
            @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;700&display=swap');

            * {
              margin: 0;
              padding: 0;
              box-sizing: border-box;
            }

            body {
              font-family: 'Cairo', 'Noto Sans Arabic', Arial, sans-serif !important;
              direction: rtl;
              background: white;
              color: #333;
              line-height: 1.6;
              font-size: 14px;
              padding: 20px;
            }

            h1, h2, h3, h4, h5, h6 {
              font-family: 'Cairo', 'Noto Sans Arabic', Arial, sans-serif !important;
              font-weight: 700;
              margin-bottom: 10px;
            }

            .print-section {
              margin-bottom: 20px;
              page-break-inside: avoid;
            }

            /* تنسيق البطاقات */
            .card {
              border: 1px solid #e2e8f0;
              border-radius: 8px;
              margin-bottom: 20px;
              background: white;
            }

            .card-header {
              padding: 16px;
              border-bottom: 1px solid #e2e8f0;
              background: #f8fafc;
            }

            .card-content {
              padding: 16px;
            }

            .card-title {
              font-size: 18px;
              font-weight: 700;
              color: #1e293b;
            }

            .card-description {
              font-size: 14px;
              color: #64748b;
              margin-top: 4px;
            }

            /* تنسيق الجدول */
            table {
              width: 100%;
              border-collapse: collapse;
              margin-bottom: 20px;
            }

            th, td {
              border: 1px solid #e2e8f0;
              padding: 12px;
              text-align: right;
            }

            th {
              background-color: #f1f5f9;
              font-weight: 700;
            }

            /* تنسيق التايم لاين */
            .timeline-event {
              margin-bottom: 16px;
              padding: 12px;
              border-left: 3px solid #3b82f6;
              background: #f8fafc;
            }

            .timeline-title {
              font-weight: 700;
              color: #1e293b;
              margin-bottom: 4px;
            }

            .timeline-date {
              font-size: 12px;
              color: #64748b;
            }

            /* إعدادات الطباعة */
            @media print {
              body {
                print-color-adjust: exact;
                -webkit-print-color-adjust: exact;
                font-size: 12px;
              }

              @page {
                size: A4;
                margin: 15mm;
              }

              .no-print {
                display: none !important;
              }

              .card {
                page-break-inside: avoid;
              }

              h1, h2, h3 {
                page-break-after: avoid;
              }
            }
          </style>
        </head>
        <body>
          <div style="text-align: center; margin-bottom: 30px; border-bottom: 2px solid #3b82f6; padding-bottom: 15px;">
            <h1 style="color: #1e293b; font-size: 24px;">${title}</h1>
            <p style="color: #64748b; font-size: 12px; margin-top: 8px;">
              تم الطباعة في: ${formatDateTime(new Date(), { arabic: true })}
            </p>
          </div>
          ${content.innerHTML}
          <script>
            // انتظار تحميل الخطوط ثم الطباعة
            document.fonts.ready.then(() => {
              setTimeout(() => {
                window.print();
                window.onafterprint = () => {
                  window.close();
                };
              }, 1500);
            });
          </script>
        </body>
      </html>
    `);

    printWindow.document.close();
    showNotification('تم تحضير الصفحة للطباعة!', 'success');

  } catch (error) {
    console.error('Error printing element:', error);
    showNotification('حدث خطأ أثناء الطباعة: ' + (error as Error).message, 'error');
  }
}

// وظيفة بديلة لطباعة البيانات مباشرة بدون الاعتماد على عنصر HTML موجود
export function printDeviceData(
  deviceInfo: any,
  timelineEvents: any[],
  title: string,
  isCustomerView: boolean = false
) {
  try {
    const printWindow = window.open('', '_blank', 'width=800,height=600');
    if (!printWindow) {
      showNotification('فشل في فتح نافذة الطباعة. يرجى التأكد من عدم حظر النوافذ المنبثقة.', 'error');
      return;
    }

    // إنشاء محتوى HTML ديناميكياً
    let htmlContent = `
      <!DOCTYPE html>
      <html dir="rtl">
        <head>
          <meta charset="utf-8" />
          <title>${title}</title>
          <style>
            @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap');
            @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;700&display=swap');

            * { margin: 0; padding: 0; box-sizing: border-box; }

            body {
              font-family: 'Cairo', 'Noto Sans Arabic', Arial, sans-serif !important;
              direction: rtl;
              background: white;
              color: #333;
              line-height: 1.6;
              font-size: 14px;
              padding: 20px;
            }

            .print-header {
              text-align: center;
              margin-bottom: 30px;
              border-bottom: 2px solid #3b82f6;
              padding-bottom: 15px;
            }

            .print-title {
              color: #1e293b;
              font-size: 24px;
              font-weight: bold;
              margin-bottom: 8px;
            }

            .print-subtitle {
              color: #64748b;
              font-size: 12px;
            }

            .info-card {
              border: 1px solid #e2e8f0;
              border-radius: 8px;
              margin-bottom: 20px;
              background: white;
            }

            .card-header {
              padding: 16px;
              border-bottom: 1px solid #e2e8f0;
              background: #f8fafc;
            }

            .card-title {
              font-size: 18px;
              font-weight: 700;
              color: #1e293b;
            }

            .card-content {
              padding: 16px;
            }

            .info-grid {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 12px;
            }

            .info-item {
              margin-bottom: 8px;
            }

            .info-label {
              font-weight: bold;
              color: #374151;
            }

            .timeline-event {
              margin-bottom: 16px;
              padding: 12px;
              border-right: 3px solid #3b82f6;
              background: #f8fafc;
            }

            .event-title {
              font-weight: bold;
              color: #1e293b;
              margin-bottom: 4px;
            }

            .event-description {
              color: #4b5563;
              margin-bottom: 4px;
            }

            .event-meta {
              font-size: 12px;
              color: #6b7280;
            }

            @media print {
              body { print-color-adjust: exact; -webkit-print-color-adjust: exact; }
              @page { size: A4; margin: 15mm; }
            }
          </style>
        </head>
        <body>
          <div class="print-header">
            <h1 class="print-title">${title}</h1>
            <p class="print-subtitle">
              تم الطباعة في: ${formatDateTime(new Date(), { arabic: true })}
            </p>
          </div>

          <div class="info-card">
            <div class="card-header">
              <h2 class="card-title">معلومات الجهاز</h2>
            </div>
            <div class="card-content">
              <div class="info-grid">
                <div class="info-item">
                  <span class="info-label">الموديل:</span> ${deviceInfo.model || '-'}
                </div>
                <div class="info-item">
                  <span class="info-label">الرقم التسلسلي:</span> ${deviceInfo.id || '-'}
                </div>
                <div class="info-item">
                  <span class="info-label">الحالة:</span> ${deviceInfo.status || '-'}
                </div>
              </div>
            </div>
          </div>`;

    // إضافة معلومات البيع للعميل
    if (isCustomerView && deviceInfo.lastSale) {
      htmlContent += `
          <div class="info-card">
            <div class="card-header">
              <h2 class="card-title">تفاصيل البيع</h2>
            </div>
            <div class="card-content">
              <div class="info-grid">
                <div class="info-item">
                  <span class="info-label">العميل:</span> ${deviceInfo.lastSale.clientName || '-'}
                </div>
                <div class="info-item">
                  <span class="info-label">فاتورة البيع:</span> ${deviceInfo.lastSale.soNumber || '-'}
                </div>
                <div class="info-item">
                  <span class="info-label">الفاتورة الرسمية:</span> ${deviceInfo.lastSale.opNumber || 'لا يوجد'}
                </div>
                <div class="info-item">
                  <span class="info-label">تاريخ البيع:</span> ${formatDate(deviceInfo.lastSale.date, { arabic: true })}
                </div>
              </div>
            </div>
          </div>`;

      // معلومات الضمان
      if (deviceInfo.warrantyInfo) {
        htmlContent += `
          <div class="info-card">
            <div class="card-header">
              <h2 class="card-title">حالة الضمان</h2>
            </div>
            <div class="card-content">
              <div class="info-grid">
                <div class="info-item">
                  <span class="info-label">الحالة:</span> ${deviceInfo.warrantyInfo.status || '-'}
                </div>
                <div class="info-item">
                  <span class="info-label">تاريخ الانتهاء:</span> ${deviceInfo.warrantyInfo.expiryDate || '-'}
                </div>
                <div class="info-item">
                  <span class="info-label">الوقت المتبقي:</span> ${deviceInfo.warrantyInfo.remaining || '-'}
                </div>
              </div>
            </div>
          </div>`;
      }
    }

    // إضافة سجل الأحداث
    if (timelineEvents && timelineEvents.length > 0) {
      htmlContent += `
          <div class="info-card">
            <div class="card-header">
              <h2 class="card-title">سجل الأحداث</h2>
            </div>
            <div class="card-content">`;

      timelineEvents.forEach(event => {
        htmlContent += `
              <div class="timeline-event">
                <div class="event-title">${event.title || '-'}</div>
                <div class="event-description">${event.description || '-'}</div>
                <div class="event-meta">
                  ${event.formattedDate || formatDate(event.date, { arabic: true })} - ${event.user || '-'}
                </div>
              </div>`;
      });

      htmlContent += `
            </div>
          </div>`;
    }

    htmlContent += `
          <script>
            document.fonts.ready.then(() => {
              setTimeout(() => {
                window.print();
                window.onafterprint = () => window.close();
              }, 1500);
            });
          </script>
        </body>
      </html>`;

    printWindow.document.write(htmlContent);
    printWindow.document.close();

    showNotification('تم تحضير الصفحة للطباعة!', 'success');

  } catch (error) {
    console.error('Error printing device data:', error);
    showNotification('حدث خطأ أثناء الطباعة: ' + (error as Error).message, 'error');
  }
}

// وظائف بديلة للتوافق مع الكود الموجود
export function exportDeviceTrackingReport(
  deviceInfo: any,
  timelineEvents: any[],
  fileName: string,
  isCustomerView: boolean = false
) {
  // استخدام طريقة Canvas المحسنة كبديل أفضل
  import('./canvas-pdf-enhanced').then(module => {
    module.createArabicPDFWithCanvas(deviceInfo, timelineEvents, fileName, isCustomerView);
  }).catch(() => {
    showNotification('تعذر تحميل وحدة Canvas، يرجى المحاولة مرة أخرى', 'error');
  });
}

export function exportDeviceTrackingReportDirect(
  deviceInfo: any,
  timelineEvents: any[],
  fileName: string,
  isCustomerView: boolean = false
) {
  // استخدام طريقة Canvas المحسنة كبديل أفضل
  import('./canvas-pdf-enhanced').then(module => {
    module.createArabicPDFWithCanvas(deviceInfo, timelineEvents, fileName, isCustomerView);
  }).catch(() => {
    showNotification('تعذر تحميل وحدة Canvas، يرجى المحاولة مرة أخرى', 'error');
  });
}
