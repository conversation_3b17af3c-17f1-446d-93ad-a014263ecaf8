import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const authResult = await requireAuth(request, 'manager');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    // إحصائيات الطلبات
    const totalPending = await prisma.employeeRequest.count({
      where: {
        status: {
          in: ['قيد المراجعة', 'قيد المراجعة المتقدمة']
        }
      }
    });

    // الطلبات المتأخرة
    const overdueRequests = await prisma.employeeRequest.findMany({
      where: {
        status: {
          in: ['قيد المراجعة', 'قيد المراجعة المتقدمة']
        },
        requestDate: {
          lt: oneDayAgo
        }
      }
    });

    // تصنيف الطلبات المتأخرة
    let overdue = 0;
    let urgent = 0;
    let critical = 0;

    overdueRequests.forEach(request => {
      const requestDate = new Date(request.requestDate);
      const hoursOverdue = (now.getTime() - requestDate.getTime()) / (1000 * 60 * 60);

      if (request.priority === 'طاريء جدا') {
        if (hoursOverdue >= 1) critical++;
        else urgent++;
      } else if (request.priority === 'طاريء') {
        if (hoursOverdue >= 4) urgent++;
        else overdue++;
      } else {
        if (hoursOverdue >= 24) overdue++;
      }
    });

    // متوسط وقت الاستجابة (بالساعات)
    const completedRequests = await prisma.employeeRequest.findMany({
      where: {
        status: {
          in: ['تم التنفيذ', 'مرفوض']
        },
        processedDate: {
          gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) // آخر أسبوع
        }
      },
      select: {
        requestDate: true,
        processedDate: true
      }
    });

    let totalResponseTime = 0;
    completedRequests.forEach(request => {
      if (request.processedDate) {
        const responseTime = (request.processedDate.getTime() - request.requestDate.getTime()) / (1000 * 60 * 60);
        totalResponseTime += responseTime;
      }
    });

    const averageResponseTime = completedRequests.length > 0 
      ? Math.round(totalResponseTime / completedRequests.length) 
      : 0;

    // معدل التصعيد
    const totalRequestsLastWeek = await prisma.employeeRequest.count({
      where: {
        requestDate: {
          gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        }
      }
    });

    const escalatedRequests = await prisma.notification.count({
      where: {
        type: 'urgent_escalation',
        createdAt: {
          gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        }
      }
    });

    const escalationRate = totalRequestsLastWeek > 0 
      ? Math.round((escalatedRequests / totalRequestsLastWeek) * 100) 
      : 0;

    const stats = {
      totalPending,
      overdue,
      urgent,
      critical,
      averageResponseTime,
      escalationRate
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error('خطأ في جلب إحصائيات التصعيد:', error);
    return NextResponse.json({ error: 'Failed to fetch escalation stats' }, { status: 500 });
  }
}
