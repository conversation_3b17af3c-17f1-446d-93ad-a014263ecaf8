const { exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);

async function testAPIEndpoints() {
  console.log('🧪 اختبار نقاط API مع view=simple...');

  const endpoints = [
    '/api/devices?view=simple',
    '/api/warehouses?view=simple', 
    '/api/sales?view=simple',
    '/api/returns?view=simple',
    '/api/suppliers?view=simple',
    '/api/device-models?view=simple',
    '/api/settings?view=simple'
  ];

  const results = [];

  for (const endpoint of endpoints) {
    try {
      console.log(`🔍 اختبار ${endpoint}...`);
      
      const command = `Invoke-RestMethod -Uri "http://localhost:9005${endpoint}" -Headers @{"Authorization"="Bearer dXNlcjphZG1pbjphZG1pbg=="}`;
      
      const { stdout, stderr } = await execAsync(command, { shell: 'powershell' });
      
      if (stderr) {
        console.log(`❌ ${endpoint}: ${stderr}`);
        results.push({ endpoint, status: 'فشل', error: stderr });
      } else {
        console.log(`✅ ${endpoint}: نجح`);
        results.push({ endpoint, status: 'نجح', error: null });
      }
      
    } catch (error) {
      console.log(`❌ ${endpoint}: ${error.message}`);
      results.push({ endpoint, status: 'فشل', error: error.message });
    }
  }

  console.log('\n📊 ملخص النتائج:');
  console.log('==================');
  
  const successful = results.filter(r => r.status === 'نجح');
  const failed = results.filter(r => r.status === 'فشل');
  
  console.log(`✅ APIs ناجحة: ${successful.length}/${results.length}`);
  console.log(`❌ APIs فاشلة: ${failed.length}/${results.length}`);

  if (successful.length > 0) {
    console.log('\n🎉 APIs الناجحة:');
    successful.forEach(result => {
      console.log(`   ✓ ${result.endpoint}`);
    });
  }

  if (failed.length > 0) {
    console.log('\n⚠️ APIs الفاشلة:');
    failed.forEach(result => {
      console.log(`   ✗ ${result.endpoint}: ${result.error}`);
    });
  }

  if (failed.length === 0) {
    console.log('\n🎊 جميع APIs تعمل بشكل مثالي!');
  }
}

testAPIEndpoints();
