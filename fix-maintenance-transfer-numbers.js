#!/usr/bin/env node

/**
 * سكريبت لإصلاح أرقام أوامر مناقلات الصيانة
 * يحدث أرقام الأوامر من MAINT- إلى MTRANS- لتجنب التضارب
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixMaintenanceTransferNumbers() {
  console.log('🔧 بدء إصلاح أرقام أوامر مناقلات الصيانة...\n');

  try {
    // البحث عن جميع أوامر الصيانة من المخزن (وليس من قسم الصيانة مباشرة)
    const warehouseMaintenanceOrders = await prisma.maintenanceOrder.findMany({
      where: {
        source: 'warehouse' // الأوامر الصادرة من المخزن
      },
      orderBy: {
        id: 'asc'
      }
    });

    console.log(`✅ تم العثور على ${warehouseMaintenanceOrders.length} أمر صيانة من المخزن`);

    if (warehouseMaintenanceOrders.length === 0) {
      console.log('ℹ️  لا توجد أوامر صيانة من المخزن تحتاج إصلاح');
      return;
    }

    // إحصائيات
    let updatedCount = 0;
    let alreadyCorrectCount = 0;

    // تحديث أرقام الأوامر
    for (const order of warehouseMaintenanceOrders) {
      if (order.orderNumber.startsWith('MAINT-')) {
        // استخراج الرقم
        const numberPart = order.orderNumber.replace('MAINT-', '');
        const newOrderNumber = `MTRANS-${numberPart}`;

        // التأكد من عدم وجود رقم مكرر
        const existingOrder = await prisma.maintenanceOrder.findFirst({
          where: {
            orderNumber: newOrderNumber,
            id: { not: order.id }
          }
        });

        if (existingOrder) {
          console.log(`⚠️  تخطي الأمر ${order.orderNumber} - الرقم الجديد ${newOrderNumber} موجود بالفعل`);
          continue;
        }

        // تحديث رقم الأمر
        await prisma.maintenanceOrder.update({
          where: { id: order.id },
          data: { orderNumber: newOrderNumber }
        });

        console.log(`✅ تم تحديث: ${order.orderNumber} → ${newOrderNumber}`);
        updatedCount++;
      } else if (order.orderNumber.startsWith('MTRANS-')) {
        alreadyCorrectCount++;
      } else {
        console.log(`ℹ️  تخطي الأمر ${order.orderNumber} - نمط غير معروف`);
      }
    }

    console.log('\n📊 ملخص العملية:');
    console.log(`   • تم تحديث: ${updatedCount} أمر`);
    console.log(`   • صحيح مسبقاً: ${alreadyCorrectCount} أمر`);
    console.log(`   • إجمالي الأوامر: ${warehouseMaintenanceOrders.length} أمر`);

    console.log('\n✅ تم إكمال إصلاح أرقام أوامر مناقلات الصيانة بنجاح!');

  } catch (error) {
    console.error('❌ خطأ أثناء إصلاح أرقام أوامر مناقلات الصيانة:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// تشغيل السكريبت
if (require.main === module) {
  fixMaintenanceTransferNumbers()
    .then(() => {
      console.log('\n🎉 تم إنجاز السكريبت بنجاح!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 فشل السكريبت:', error);
      process.exit(1);
    });
}

module.exports = { fixMaintenanceTransferNumbers };
