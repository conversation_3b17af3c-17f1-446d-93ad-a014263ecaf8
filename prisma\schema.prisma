// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ===== نماذج الصلاحيات المحسنة =====

model Permission {
  id          Int      @id @default(autoincrement())
  name        String   @unique // dashboard, sales, inventory, etc.
  displayName String   // الاسم المعروض
  category    String   // الفئة: core, sales, maintenance, etc.
  description String?  // وصف الصلاحية
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  userPermissions UserPermission[]
  
  @@map("permissions")
}

model UserPermission {
  id           Int        @id @default(autoincrement())
  userId       Int
  permissionId Int
  canView      Boolean    @default(false)
  canCreate    Boolean    @default(false)
  canEdit      <PERSON>olean    @default(false)
  canDelete    Boolean    @default(false)
  canViewAll   Boolean    @default(false)
  canManage    Boolean    @default(false)
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  
  user         User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  permission   Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  
  @@unique([userId, permissionId])
  @@map("user_permissions")
}

model UserWarehouseAccess {
  id          Int       @id @default(autoincrement())
  userId      Int
  warehouseId Int
  accessType  String    @default("read") // read, write, admin
  canTransfer Boolean   @default(false)
  canAudit    Boolean   @default(false)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  warehouse   Warehouse @relation(fields: [warehouseId], references: [id], onDelete: Cascade)
  
  @@unique([userId, warehouseId])
  @@map("user_warehouse_access")
}

// ===== نماذج الاستبدال المحسنة =====

model DeviceReplacement {
  id                  Int      @id @default(autoincrement())
  originalDeviceId    String
  replacementDeviceId String
  reason              String
  replacementDate     DateTime @default(now())
  notes               String?
  status              String   @default("active") // active, reverted
  processedBy         String?  // الموظف الذي قام بالاستبدال
  approvedBy          String?  // الموظف الذي وافق على الاستبدال
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
  
  originalDevice      Device   @relation("OriginalDevice", fields: [originalDeviceId], references: [id])
  replacementDevice   Device   @relation("ReplacementDevice", fields: [replacementDeviceId], references: [id])
  
  @@unique([originalDeviceId, replacementDeviceId])
  @@map("device_replacements")
}

// ===== نماذج الرسائل المحسنة =====

model MessageRecipient {
  id        Int             @id @default(autoincrement())
  messageId Int
  userId    Int
  isRead    Boolean         @default(false)
  readAt    DateTime?
  
  message   InternalMessage @relation(fields: [messageId], references: [id], onDelete: Cascade)
  user      User            @relation(fields: [userId], references: [id])
  
  @@unique([messageId, userId])
  @@map("message_recipients")
}

model User {
  id              Int      @id @default(autoincrement())
  email           String   @unique
  name            String?
  username        String?  @unique @default("user")
  role            String?  @default("user")
  phone           String?  @default("")
  photo           String?  @default("")
  status          String?  @default("Active")
  lastLogin       DateTime?
  branchLocation  String?  // موقع الفرع
  warehouseAccess Json?    // صلاحيات الوصول للمخازن كـ JSON array - سيتم إزالتها لاحقاً
  permissions     Json?    // للصلاحيات المخزنة كـ JSON - سيتم إزالتها لاحقاً
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt @default(now())
  
  // العلاقات الحالية
  posts             Post[]
  createdDatabases  Database[]
  supplyOrderDrafts SupplyOrderDraft[]
  
  // العلاقات الجديدة المحسنة
  userPermissions     UserPermission[]
  userWarehouseAccess UserWarehouseAccess[]
  messageRecipients   MessageRecipient[]
  notifications       Notification[]
  responseTemplates   ResponseTemplate[]

  @@map("users")
}

model Post {
  id        Int     @id @default(autoincrement())
  title     String
  content   String?
  published Boolean @default(false)
  author    User    @relation(fields: [authorId], references: [id])
  authorId  Int
}

model SystemSetting {
  id              Int      @id @default(1)
  logoUrl         String   @default("")
  companyNameAr   String   @default("")
  companyNameEn   String   @default("")
  addressAr       String   @default("")
  addressEn       String   @default("")
  phone           String   @default("")
  email           String   @default("")
  website         String   @default("")
  footerTextAr    String   @default("")
  footerTextEn    String   @default("")
  updatedAt       DateTime @updatedAt @default(now())
  createdAt       DateTime @default(now())
}

model DeviceModel {
  id             Int      @id @default(autoincrement())
  name           String
  manufacturerId BigInt
  category       String   @default("هاتف ذكي")
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@unique([name, manufacturerId])
}

model AuditLog {
  id        Int      @id @default(autoincrement())
  timestamp DateTime @default(now())
  userId    Int
  username  String
  operation String
  details   String
}

model SupplyOrder {
  id             Int      @id @default(autoincrement())
  supplyOrderId  String   @unique
  supplierId     Int
  invoiceNumber  String?  // جعل رقم الفاتورة اختياري
  supplyDate     DateTime // ✅ تحويل من String إلى DateTime
  warehouseId    Int
  employeeName   String
  notes          String?
  invoiceFileName String?
  referenceNumber String?
  createdAt      DateTime @default(now())
  status         String?  @default("completed")

  // العلاقة مع العناصر
  items          SupplyOrderItem[]
}

model SupplyOrderItem {
  id           Int      @id @default(autoincrement())
  supplyOrderId Int
  imei         String
  model        String
  manufacturer String
  condition    String   // 'جديد' | 'مستخدم'
  createdAt    DateTime @default(now())

  // العلاقة مع أمر التوريد
  supplyOrder  SupplyOrder @relation(fields: [supplyOrderId], references: [id], onDelete: Cascade)

  @@map("supply_order_items")
}

// جدول المسودات لأوامر التوريد
model SupplyOrderDraft {
  id            Int      @id @default(autoincrement())
  userId        Int      // معرف المستخدم الذي أنشأ المسودة
  formState     String   // بيانات النموذج محفوظة كـ JSON
  currentItems  String   // قائمة الأجهزة محفوظة كـ JSON
  attachments   String?  // المرفقات محفوظة كـ JSON
  supplyOrderId String?  // رقم أمر التوريد إذا كان محدداً
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // العلاقة مع المستخدم
  user          User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("supply_order_drafts")
}

model Sale {
  id             Int      @id @default(autoincrement())
  soNumber       String   @unique
  opNumber       String
  date           DateTime // ✅ تحويل من String إلى DateTime
  clientName     String
  warehouseName  String
  notes          String?
  warrantyPeriod String
  employeeName   String
  createdAt      DateTime @default(now())
  attachments    String?

  // العلاقة مع العناصر
  items          SaleItem[]
}

model SaleItem {
  id        Int      @id @default(autoincrement())
  saleId    Int
  deviceId  String
  model     String
  price     Float
  condition String   // 'جديد' | 'مستخدم'
  createdAt DateTime @default(now())

  // العلاقة مع المبيعة
  sale      Sale @relation(fields: [saleId], references: [id], onDelete: Cascade)

  @@map("sale_items")
}

model Device {
  id          String   @id
  model       String
  status      String
  storage     String
  price       Float
  condition   String
  warehouseId Int?
  supplierId  Int?
  dateAdded   DateTime @default(now())
  replacementInfo Json?  // معلومات الاستبدال كـ JSON - سيتم إزالتها لاحقاً
  
  // العلاقات الجديدة المحسنة للاستبدال
  originalReplacements    DeviceReplacement[] @relation("OriginalDevice")
  replacementFor         DeviceReplacement[] @relation("ReplacementDevice")
}

model Warehouse {
  id        Int      @id @default(autoincrement())
  name      String
  type      String
  location  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt @default(now())
  
  // العلاقات الجديدة المحسنة
  userAccess UserWarehouseAccess[]
}

model Return {
  id             Int       @id @default(autoincrement())
  roNumber       String    @unique
  opReturnNumber String
  date           DateTime  // ✅ تحويل من String إلى DateTime
  saleId         Int
  soNumber       String
  clientName     String
  warehouseName  String
  notes          String?
  status         String    @default("معلق") // معلق، مقبول، مرفوض، مكتمل
  processedBy    String?
  processedDate  DateTime? // ✅ تحويل من String إلى DateTime
  employeeName   String
  createdAt      DateTime  @default(now())
  attachments    String?

  // العلاقة مع العناصر
  items          ReturnItem[]
}

model ReturnItem {
  id                   Int      @id @default(autoincrement())
  returnId             Int
  deviceId             String
  model                String
  returnReason         String
  replacementDeviceId  String?
  isReplacement        Boolean  @default(false)
  originalDeviceId     String?
  createdAt            DateTime @default(now())

  // العلاقة مع المرتجع
  return               Return @relation(fields: [returnId], references: [id], onDelete: Cascade)

  @@map("return_items")
}

model EvaluationOrder {
  id              Int       @id @default(autoincrement())
  orderId         String    @unique
  employeeName    String
  date            DateTime  // ✅ تحويل من String إلى DateTime
  notes           String?
  status          String    @default("معلق")
  acknowledgedBy  String?
  acknowledgedDate DateTime? // ✅ تحويل من String إلى DateTime
  warehouseName   String?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt @default(now())

  // العلاقة مع العناصر
  items           EvaluationOrderItem[]

  @@map("evaluation_orders")
}

model EvaluationOrderItem {
  id              Int      @id @default(autoincrement())
  evaluationOrderId Int
  deviceId        String
  model           String
  externalGrade   String
  screenGrade     String
  networkGrade    String
  finalGrade      String
  fault           String?
  damageType      String?
  createdAt       DateTime @default(now())

  // العلاقة مع أمر التقييم
  evaluationOrder EvaluationOrder @relation(fields: [evaluationOrderId], references: [id], onDelete: Cascade)

  @@map("evaluation_order_items")
}

model Client {
  id        Int      @id @default(autoincrement())
  name      String
  phone     String?
  email     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Supplier {
  id        Int      @id @default(autoincrement())
  name      String
  phone     String?
  email     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model MaintenanceOrder {
  id                      Int      @id @default(autoincrement())
  orderNumber             String   @unique
  referenceNumber         String?
  date                    DateTime // ✅ تحويل من String إلى DateTime
  employeeName            String
  maintenanceEmployeeId   Int?
  maintenanceEmployeeName String?
  notes                   String?
  attachmentName          String?
  status                  String   @default("wip") // wip, completed
  source                  String   @default("warehouse") // warehouse, direct
  items                   String?  // JSON string of items
  createdAt               DateTime @default(now())

  // العلاقة مع العناصر (للاستخدام المستقبلي)
  itemsRelation           MaintenanceOrderItem[] @relation("MaintenanceOrderItems")
}

model MaintenanceOrderItem {
  id                 Int      @id @default(autoincrement())
  maintenanceOrderId Int
  deviceId           String
  model              String
  fault              String?
  notes              String?
  createdAt          DateTime @default(now())

  // العلاقة مع أمر الصيانة
  maintenanceOrder   MaintenanceOrder @relation("MaintenanceOrderItems", fields: [maintenanceOrderId], references: [id], onDelete: Cascade)

  @@map("maintenance_order_items")
}

model MaintenanceReceiptOrder {
  id                      Int      @id @default(autoincrement())
  receiptNumber           String   @unique
  referenceNumber         String?
  date                    DateTime // ✅ تحويل من String إلى DateTime
  employeeName            String
  maintenanceEmployeeName String?
  notes                   String?
  attachmentName          String?
  status                  String   @default("completed") // completed
  items                   String?  // JSON string of items
  createdAt               DateTime @default(now())

  // العلاقة مع العناصر (للاستخدام المستقبلي)
  itemsRelation           MaintenanceReceiptOrderItem[] @relation("MaintenanceReceiptOrderItems")
}

model MaintenanceReceiptOrderItem {
  id                        Int      @id @default(autoincrement())
  maintenanceReceiptOrderId Int
  deviceId                  String
  model                     String
  result                    String
  fault                     String?
  damage                    String?
  notes                     String?
  createdAt                 DateTime @default(now())

  // العلاقة مع إيصال الصيانة
  maintenanceReceiptOrder   MaintenanceReceiptOrder @relation("MaintenanceReceiptOrderItems", fields: [maintenanceReceiptOrderId], references: [id], onDelete: Cascade)

  @@map("maintenance_receipt_order_items")
}

model DeliveryOrder {
  id                  Int      @id @default(autoincrement())
  deliveryOrderNumber String   @unique
  referenceNumber     String?
  date                DateTime // ✅ تحويل من String إلى DateTime
  warehouseId         Int
  warehouseName       String
  employeeName        String
  notes               String?
  attachmentName      String?
  status              String   @default("completed")
  createdAt           DateTime @default(now())

  // العلاقة مع العناصر
  items               DeliveryOrderItem[]
}

model DeliveryOrderItem {
  id              Int      @id @default(autoincrement())
  deliveryOrderId Int
  deviceId        String
  model           String
  result          String
  fault           String?
  damage          String?
  notes           String?
  createdAt       DateTime @default(now())

  // العلاقة مع أمر التسليم
  deliveryOrder   DeliveryOrder @relation(fields: [deliveryOrderId], references: [id], onDelete: Cascade)

  @@map("delivery_order_items")
}

model MaintenanceLog {
  id              Int       @id @default(autoincrement())
  deviceId        String
  model           String
  repairDate      DateTime  // ✅ تحويل من String إلى DateTime
  notes           String?
  result          String?
  status          String    @default("pending") // pending, acknowledged
  acknowledgedDate DateTime? // ✅ تحويل من String إلى DateTime
  warehouseName   String?
  acknowledgedBy  String?
  createdAt       DateTime  @default(now())

  @@map("maintenance_logs")
}

model EmployeeRequest {
  id                   Int       @id @default(autoincrement())
  requestNumber        String    @unique
  requestType          String    // تعديل، حذف، إعادة نظر
  priority             String    // عادي، طاريء، طاريء جدا
  notes                String
  status               String    @default("قيد المراجعة") // قيد المراجعة، قيد المراجعة المتقدمة، تم التنفيذ، مرفوض
  requestDate          DateTime  // ✅ تحويل من String إلى DateTime
  employeeName         String
  employeeId           Int
  relatedOrderType     String?   // sales, returns, supply, etc.
  relatedOrderId       Int?
  relatedOrderDisplayId String?  // رقم الأمر المعروض للمستخدم
  attachmentName       String?   // للتوافق مع النظام القديم
  attachments          Json?     // مرفقات متعددة جديدة
  adminNotes           String?
  processedBy          Int?
  processedDate        DateTime? // ✅ تحويل من String إلى DateTime
  tags                 Json?     // علامات للبحث والفلترة
  isArchived           Boolean   @default(false) // للأرشفة
  archivedAt           DateTime? // تاريخ الأرشفة
  searchVector         String?   // للبحث النصي المحسن
  createdAt            DateTime  @default(now())
  updatedAt            DateTime  @updatedAt @default(now())

  // العلاقات
  comments             RequestComment[]
  attachmentFiles      RequestAttachment[]

  @@map("employee_requests")
}

model RequestAttachment {
  id            Int             @id @default(autoincrement())
  requestId     Int             // معرف الطلب
  fileName      String          // اسم الملف الأصلي
  fileType      String          // نوع الملف (image, document, video, etc.)
  fileSize      Int             // حجم الملف بالبايت
  filePath      String          // مسار الملف المحفوظ
  fileUrl       String?         // رابط الملف (للملفات السحابية)
  mimeType      String          // نوع MIME
  uploadedBy    Int             // معرف المستخدم الذي رفع الملف
  uploadedAt    DateTime        @default(now())
  isDeleted     Boolean         @default(false)
  deletedAt     DateTime?

  // العلاقات
  request       EmployeeRequest @relation(fields: [requestId], references: [id], onDelete: Cascade)

  @@map("request_attachments")
}

model RequestComment {
  id                Int             @id @default(autoincrement())
  requestId         Int             // معرف الطلب
  userId            Int             // معرف المستخدم (موظف أو إدارة)
  userName          String          // اسم المستخدم
  userRole          String          // دور المستخدم (employee, manager, admin)
  comment           String          // نص التعليق
  commentType       String          @default("comment") // comment, clarification_request, clarification_response
  attachments       Json?           // مرفقات التعليق
  isInternal        Boolean         @default(false) // هل التعليق داخلي (للإدارة فقط)
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt

  // العلاقات
  request           EmployeeRequest @relation(fields: [requestId], references: [id], onDelete: Cascade)

  @@map("request_comments")
}

model ResponseTemplate {
  id            Int      @id @default(autoincrement())
  name          String   // اسم القالب
  category      String   // approval, rejection, clarification, custom
  title         String   // عنوان الرد
  content       String   // محتوى القالب مع متغيرات
  variables     Json?    // المتغيرات المتاحة في القالب
  isSystem      Boolean  @default(false) // هل هو قالب نظام أم مخصص
  isActive      Boolean  @default(true)  // هل القالب نشط
  usageCount    Int      @default(0)     // عدد مرات الاستخدام
  createdBy     Int?     // منشئ القالب
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // العلاقات
  creator       User?    @relation(fields: [createdBy], references: [id], onDelete: SetNull)

  @@map("response_templates")
}

model Notification {
  id            Int       @id @default(autoincrement())
  userId        Int       // معرف المستخدم المستقبل
  type          String    // new_request, overdue, urgent_escalation, response, clarification
  title         String    // عنوان الإشعار
  message       String    // محتوى الإشعار
  requestId     Int?      // معرف الطلب المرتبط
  requestNumber String?   // رقم الطلب
  employeeName  String?   // اسم الموظف
  priority      String?   // أولوية الطلب
  actionRequired Boolean  @default(false) // هل يتطلب إجراء
  read          Boolean   @default(false) // هل تم قراءته
  readAt        DateTime? // وقت القراءة
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // العلاقة مع المستخدم
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

model InternalMessage {
  id                  Int       @id @default(autoincrement())
  threadId            Int       // معرف المحادثة
  senderId            Int       // معرف المرسل
  senderName          String    // اسم المرسل
  recipientId         Int       // معرف المستقبل (0 للجميع)
  recipientName       String    // اسم المستقبل
  recipientIds        Json?     // قائمة معرفات المستقبلين للمجموعات - سيتم إزالتها لاحقاً
  text                String    // نص الرسالة
  attachmentName      String?   // اسم المرفق
  attachmentContent   String?   // محتوى المرفق Base64 (للصور الصغيرة)
  attachmentType      String?   // نوع المرفق
  attachmentUrl       String?   // رابط المرفق
  attachmentFileName  String?   // اسم الملف المحفوظ
  attachmentSize      Int?      // حجم المرفق
  sentDate            DateTime  // ✅ تحويل من String إلى DateTime
  status              String    @default("مرسلة") // مرسلة، مقروءة، تم الرد، تم الحل
  isRead              Boolean   @default(false)
  parentMessageId     Int?      // معرف الرسالة الأصلية للردود
  employeeRequestId   Int?      // ربط بطلب موظف
  resolutionNote      String?   // ملاحظة الحل
  createdAt           DateTime  @default(now())
  updatedAt           DateTime  @updatedAt @default(now())

  // العلاقات الجديدة المحسنة
  recipients          MessageRecipient[]

  @@map("internal_messages")
}

// نماذج جديدة لإدارة قواعد البيانات
model DatabaseConnection {
  id          Int      @id @default(autoincrement())
  name        String   @unique
  host        String
  port        Int      @default(5432)
  database    String
  username    String
  password    String   // مشفر
  isActive    Boolean  @default(false)
  isDefault   Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  backups     DatabaseBackup[]
  databases   Database[]
  
  @@map("database_connections")
}

model DatabaseBackup {
  id           Int                @id @default(autoincrement())
  name         String
  description  String?
  filePath     String
  fileSize     String
  backupType   String             @default("manual") // manual, automatic
  status       String             @default("completed") // pending, completed, failed
  createdBy    String?
  createdAt    DateTime           @default(now())
  
  connection   DatabaseConnection @relation(fields: [connectionId], references: [id], onDelete: Cascade)
  connectionId Int
  
  @@map("database_backups")
}

model Database {
  id           Int                @id @default(autoincrement())
  name         String
  connectionId Int
  owner        String             @default("")
  template     String             @default("template0")
  encoding     String             @default("UTF8")
  createdBy    Int
  createdAt    DateTime           @default(now())
  updatedAt    DateTime           @updatedAt @default(now())
  
  // العلاقات
  connection   DatabaseConnection @relation(fields: [connectionId], references: [id], onDelete: Cascade)
  creator      User               @relation(fields: [createdBy], references: [id])
  
  @@unique([name, connectionId])
  @@map("databases")
}

// نموذج التحويلات المخزنية
model WarehouseTransfer {
  id                 Int       @id @default(autoincrement())
  transferNumber     String    @unique
  fromWarehouse      String
  toWarehouse        String
  deviceId           String
  model              String
  status             String    @default("معلق") // معلق، في الطريق، مكتمل، ملغي
  requestedBy        String
  approvedBy         String?
  transferredBy      String?
  receivedBy         String?
  requestDate        DateTime  @default(now())
  approvalDate       DateTime?
  transferDate       DateTime?
  receiveDate        DateTime?
  notes              String?
  reason             String?
  priority           String    @default("عادي") // عاجل، عادي، منخفض
  attachments        String?
  createdAt          DateTime  @default(now())
  updatedAt          DateTime  @updatedAt
  
  @@map("warehouse_transfers")
}
