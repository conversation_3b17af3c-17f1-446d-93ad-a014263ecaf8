const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testRawQuery() {
  try {
    console.log('اختبار insert مباشر في قاعدة البيانات...');

    // محاولة insert مباشرة
    const result = await prisma.$executeRaw`
      INSERT INTO "DeliveryOrder" 
      (
        "deliveryOrderNumber", 
        "date", 
        "warehouseId", 
        "warehouseName", 
        "employeeName", 
        "status"
      ) 
      VALUES 
      (
        'TEST-DIRECT', 
        NOW(), 
        1, 
        'test warehouse', 
        'test employee', 
        'completed'
      )
    `;

    console.log('✅ تم Insert مباشر بنجاح:', result);

    // الحصول على السجل المُدرج
    const inserted = await prisma.deliveryOrder.findUnique({
      where: { deliveryOrderNumber: 'TEST-DIRECT' }
    });

    console.log('السجل المُدرج:', inserted);

    // حذف السجل
    await prisma.deliveryOrder.delete({
      where: { deliveryOrderNumber: 'TEST-DIRECT' }
    });

    console.log('✅ تم حذف السجل');

  } catch (error) {
    console.error('❌ خطأ في raw query:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testRawQuery();
