import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function createWarehouseTransferTable() {
  try {
    console.log('إنشاء جدول التحويلات المخزنية...');

    // إنشاء الجدول مباشرة في قاعدة البيانات
    await prisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS "warehouse_transfers" (
        "id" SERIAL PRIMARY KEY,
        "transferNumber" VARCHAR(255) UNIQUE NOT NULL,
        "fromWarehouse" VARCHAR(255) NOT NULL,
        "toWarehouse" VARCHAR(255) NOT NULL,
        "deviceId" VARCHAR(255) NOT NULL,
        "model" VARCHAR(255) NOT NULL,
        "status" VARCHAR(255) NOT NULL DEFAULT 'معلق',
        "requestedBy" VARCHAR(255) NOT NULL,
        "approvedBy" VARCHAR(255),
        "transferredBy" VARCHAR(255),
        "receivedBy" VARCHAR(255),
        "requestDate" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "approvalDate" TIMESTAMP,
        "transferDate" TIMESTAMP,
        "receiveDate" TIMESTAMP,
        "notes" TEXT,
        "reason" TEXT,
        "priority" VARCHAR(255) NOT NULL DEFAULT 'عادي',
        "attachments" TEXT,
        "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
      )
    `;

    console.log('تم إنشاء جدول التحويلات المخزنية بنجاح');

    // إضافة بعض البيانات التجريبية
    await prisma.$executeRaw`
      INSERT INTO "warehouse_transfers" (
        "transferNumber", "fromWarehouse", "toWarehouse", "deviceId", "model", 
        "requestedBy", "reason", "notes"
      ) VALUES 
      ('TRF-001', 'المخزن الرئيسي', 'مخزن الصيانة', '222222222225555', 'Samsung Galaxy', 'أحمد محمد', 'للصيانة', 'تحويل للصيانة العاجلة'),
      ('TRF-002', 'مخزن الصيانة', 'المخزن الرئيسي', '333333333336666', 'iPhone 13', 'سارة أحمد', 'انتهاء الصيانة', 'تم الانتهاء من الصيانة')
      ON CONFLICT ("transferNumber") DO NOTHING
    `;

    console.log('تم إضافة البيانات التجريبية');

  } catch (error) {
    console.error('خطأ في إنشاء جدول التحويلات المخزنية:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createWarehouseTransferTable();
