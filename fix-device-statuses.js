const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixDeviceStatuses() {
  try {
    console.log('🔧 إصلاح حالات الأجهزة العالقة...\n');

    // البحث عن الأجهزة ذات الحالات المرتبطة بالصيانة
    const devicesWithMaintenanceStatus = await prisma.device.findMany({
      where: {
        status: {
          in: ['مرسل للصيانة', 'قيد الإصلاح', 'بانتظار استلام في المخزن']
        }
      },
      select: {
        id: true,
        model: true,
        status: true
      }
    });

    console.log(`📱 الأجهزة ذات حالات الصيانة: ${devicesWithMaintenanceStatus.length}\n`);

    if (devicesWithMaintenanceStatus.length === 0) {
      console.log('✅ لا توجد أجهزة عالقة في حالات الصيانة');
      return;
    }

    // فحص كل جهاز
    for (const device of devicesWithMaintenanceStatus) {
      console.log(`🔍 فحص جهاز ${device.id} - ${device.model}`);
      console.log(`   الحالة الحالية: ${device.status}`);

      // البحث عن أوامر صيانة تحتوي على هذا الجهاز
      const maintenanceOrders = await prisma.maintenanceOrder.findMany({
        where: {
          items: {
            contains: device.id
          }
        },
        select: {
          id: true,
          orderNumber: true,
          status: true,
          items: true
        }
      });

      console.log(`   موجود في ${maintenanceOrders.length} أمر صيانة`);

      let shouldResetStatus = false;
      let newStatus = 'متاح';

      if (maintenanceOrders.length === 0) {
        // الجهاز ليس في أي أمر صيانة، يجب إعادة تعيين حالته
        shouldResetStatus = true;
        newStatus = 'متاح';
        console.log(`   ❌ غير موجود في أي أمر صيانة - سيتم إعادة تعيين إلى: ${newStatus}`);
      } else {
        // فحص ما إذا كانت جميع أوامر الصيانة مكتملة
        const activeOrders = maintenanceOrders.filter(order => order.status !== 'completed');
        if (activeOrders.length === 0) {
          shouldResetStatus = true;
          newStatus = 'متاح';
          console.log(`   ✅ جميع أوامر الصيانة مكتملة - سيتم إعادة تعيين إلى: ${newStatus}`);
        } else {
          console.log(`   ⏳ يوجد ${activeOrders.length} أمر صيانة نشط`);
        }
      }

      // تطبيق التغيير
      if (shouldResetStatus) {
        await prisma.device.update({
          where: { id: device.id },
          data: { 
            status: newStatus
          }
        });
        console.log(`   ✅ تم تحديث حالة الجهاز إلى: ${newStatus}`);
      }

      console.log('');
    }

    console.log('🎉 تم الانتهاء من إصلاح حالات الأجهزة');

  } catch (error) {
    console.error('خطأ:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixDeviceStatuses();
