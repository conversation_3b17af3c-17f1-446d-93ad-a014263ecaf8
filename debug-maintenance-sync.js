const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function debugMaintenanceOrderSync() {
  try {
    console.log('🔍 تشخيص تطابق أوامر الصيانة بين قاعدة البيانات والواجهة...\n');

    // جلب جميع أوامر الصيانة من قاعدة البيانات
    const dbOrders = await prisma.maintenanceOrder.findMany({
      select: {
        id: true,
        orderNumber: true,
        date: true,
        status: true,
        employeeName: true,
        items: true
      },
      orderBy: {
        date: 'desc'
      }
    });

    console.log('📊 أوامر الصيانة في قاعدة البيانات:');
    console.log('========================================');
    
    if (dbOrders.length === 0) {
      console.log('❌ لا توجد أوامر صيانة في قاعدة البيانات');
    } else {
      dbOrders.forEach(order => {
        console.log(`🔧 ${order.orderNumber} (ID: ${order.id})`);
        console.log(`   التاريخ: ${order.date}`);
        console.log(`   الحالة: ${order.status}`);
        console.log(`   الموظف: ${order.employeeName}`);
        
        // تحليل items
        try {
          const items = order.items ? JSON.parse(order.items) : [];
          console.log(`   عدد الأجهزة: ${items.length}`);
          if (items.length > 0) {
            const deviceIds = items.map(item => item.deviceId || item.id).filter(Boolean);
            console.log(`   الأجهزة: [${deviceIds.join(', ')}]`);
          }
        } catch (error) {
          console.log(`   ❌ خطأ في تحليل items: ${error.message}`);
        }
        console.log('');
      });
    }

    // محاولة استدعاء API للتحقق
    console.log('\n🌐 اختبار API endpoints:');
    console.log('========================');
    
    // محاولة جلب البيانات من API
    try {
      const response = await fetch('http://localhost:9005/api/maintenance-orders', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        const apiOrders = await response.json();
        console.log(`✅ API يعيد ${apiOrders.length} أمر صيانة`);
        
        // مقارنة IDs
        const dbIds = dbOrders.map(o => o.id).sort();
        const apiIds = apiOrders.map(o => o.id).sort();
        
        console.log(`   قاعدة البيانات IDs: [${dbIds.join(', ')}]`);
        console.log(`   API IDs: [${apiIds.join(', ')}]`);
        
        if (JSON.stringify(dbIds) === JSON.stringify(apiIds)) {
          console.log('✅ IDs متطابقة');
        } else {
          console.log('❌ IDs غير متطابقة!');
        }
      } else {
        console.log(`❌ API خطأ: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      console.log(`❌ خطأ في الاتصال بـ API: ${error.message}`);
    }

    // اختبار حذف كل ID موجود
    console.log('\n🗑️ اختبار إمكانية الحذف:');
    console.log('===========================');
    
    for (const order of dbOrders) {
      try {
        const response = await fetch(`http://localhost:9005/api/maintenance-orders/${order.id}`, {
          method: 'HEAD' // فقط للتحقق من وجود الـ endpoint
        });
        console.log(`🔧 ${order.orderNumber} (ID: ${order.id}): ${response.status === 404 ? '❌ غير موجود' : '✅ موجود'}`);
      } catch (error) {
        console.log(`🔧 ${order.orderNumber} (ID: ${order.id}): ❌ خطأ في الاتصال`);
      }
    }

    // التحقق من الأجهزة في أوامر الصيانة
    console.log('\n📱 التحقق من حالات الأجهزة:');
    console.log('===============================');
    
    for (const order of dbOrders) {
      try {
        const items = order.items ? JSON.parse(order.items) : [];
        if (items.length > 0) {
          console.log(`\n🔧 أمر ${order.orderNumber}:`);
          for (const item of items) {
            const deviceId = item.deviceId || item.id;
            if (deviceId) {
              const device = await prisma.device.findUnique({
                where: { id: deviceId },
                select: { id: true, model: true, status: true }
              });
              
              if (device) {
                console.log(`   📱 ${device.id} - ${device.model} - حالة: ${device.status}`);
              } else {
                console.log(`   ❌ جهاز ${deviceId} غير موجود في قاعدة البيانات`);
              }
            }
          }
        }
      } catch (error) {
        console.log(`   ❌ خطأ في معالجة أمر ${order.orderNumber}: ${error.message}`);
      }
    }

  } catch (error) {
    console.error('خطأ عام:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugMaintenanceOrderSync();
