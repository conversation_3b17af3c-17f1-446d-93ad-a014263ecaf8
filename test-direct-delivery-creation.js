const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// دالة لتنظيف البيانات من null bytes (محسنة)
function sanitizeString(str) {
  if (!str) return str;
  if (typeof str !== 'string') return str;
  
  // إزالة null bytes وأحرف التحكم الأخرى
  let cleaned = str
    .replace(/\x00/g, '') // إزالة null bytes
    .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // إزالة أحرف التحكم
    .trim();
    
  return cleaned.length > 0 ? cleaned : null;
}

// دالة لتنظيف بيانات أمر التسليم (محسنة)
function sanitizeDeliveryOrderData(data) {
  const sanitized = {};
  
  // نسخ جميع الحقول مع التنظيف
  for (const [key, value] of Object.entries(data)) {
    if (typeof value === 'string') {
      sanitized[key] = sanitizeString(value);
    } else {
      sanitized[key] = value;
    }
  }
  
  return sanitized;
}

async function testDirectDeliveryOrderCreation() {
  try {
    console.log('اختبار إنشاء أمر تسليم مباشر مع قاعدة البيانات...');

    // الحصول على مخزن
    const warehouse = await prisma.warehouse.findFirst();
    if (!warehouse) {
      console.log('لا يوجد مخازن في النظام');
      return;
    }

    // الحصول على جهاز قيد الصيانة
    const maintenanceDevice = await prisma.device.findFirst({
      where: { status: 'قيد الإصلاح' }
    });

    if (!maintenanceDevice) {
      console.log('لا توجد أجهزة قيد الصيانة للاختبار');
      return;
    }

    console.log(`الجهاز المختار: ${maintenanceDevice.id} (${maintenanceDevice.model})`);

    // إنشاء رقم أمر التسليم
    const deliveryOrderNumber = `DEL-${Date.now()}`;

    // بيانات أمر التسليم مع تنظيف محتمل للـ null bytes
    const rawData = {
      deliveryOrderNumber,
      referenceNumber: null,
      date: new Date(),
      warehouseId: warehouse.id,
      warehouseName: warehouse.name || 'مخزن غير محدد',
      employeeName: 'مختبر النظام \x00 مع null byte', // إضافة null byte للاختبار
      notes: 'اختبار إنشاء أمر تسليم \x00\x01\x02 مع أحرف تحكم',
      status: 'completed',
      attachmentName: null
    };

    console.log('البيانات الخام قبل التنظيف:');
    for (const [key, value] of Object.entries(rawData)) {
      if (typeof value === 'string') {
        console.log(`${key}: "${value}" (contains null byte: ${value.includes('\x00')})`);
      }
    }

    const deliveryOrderData = sanitizeDeliveryOrderData(rawData);

    console.log('\nالبيانات بعد التنظيف:');
    for (const [key, value] of Object.entries(deliveryOrderData)) {
      if (typeof value === 'string') {
        console.log(`${key}: "${value}" (contains null byte: ${value.includes('\x00')})`);
      }
    }

    // إنشاء أمر التسليم
    const order = await prisma.deliveryOrder.create({
      data: deliveryOrderData
    });

    console.log('✅ تم إنشاء أمر التسليم:', order.id);

    // إنشاء عنصر أمر التسليم
    const sanitizedItemData = {
      deliveryOrderId: order.id,
      deviceId: sanitizeString(maintenanceDevice.id) || '',
      model: sanitizeString(maintenanceDevice.model || '') || '',
      result: sanitizeString('تم الإصلاح بنجاح \x00 مع null byte') || '',
      fault: sanitizeString('مشكلة في البرمجيات \x01'),
      damage: null,
      notes: sanitizeString('تم إصلاح الجهاز وجاهز للتسليم \x00\x02')
    };

    console.log('بيانات عنصر أمر التسليم المنظفة:', sanitizedItemData);

    const orderItem = await prisma.deliveryOrderItem.create({
      data: sanitizedItemData
    });

    console.log('✅ تم إنشاء عنصر أمر التسليم:', orderItem.id);

    // إنشاء سجل صيانة
    const maintenanceLogData = {
      deviceId: sanitizeString(maintenanceDevice.id) || '',
      model: sanitizeString(maintenanceDevice.model || '') || '',
      repairDate: new Date().toISOString(),
      notes: sanitizeString('تم إنهاء الصيانة \x00 مع null byte') || '',
      result: sanitizeString('تم الإصلاح بنجاح \x01') || '',
      status: 'pending'
    };

    console.log('بيانات سجل الصيانة المنظفة:', maintenanceLogData);

    const maintenanceLog = await prisma.maintenanceLog.create({
      data: maintenanceLogData
    });

    console.log('✅ تم إنشاء سجل الصيانة:', maintenanceLog.id);

    // تحديث حالة الجهاز
    await prisma.device.update({
      where: { id: maintenanceDevice.id },
      data: { status: 'متاح للبيع' }
    });

    console.log('✅ تم تحديث حالة الجهاز إلى متاح للبيع');

    console.log('\n🎉 تم إنجاز جميع العمليات بنجاح! لا توجد مشاكل في null bytes');

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error);
    
    if (error.message.includes('invalid byte sequence')) {
      console.error('المشكلة: null bytes ما زالت موجودة في البيانات');
    } else if (error.message.includes('violates')) {
      console.error('المشكلة: انتهاك قيود قاعدة البيانات');
    }
  } finally {
    await prisma.$disconnect();
  }
}

testDirectDeliveryOrderCreation();
