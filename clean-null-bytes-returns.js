const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// دالة لتنظيف البيانات من null bytes
function sanitizeString(str) {
  if (!str || typeof str !== 'string') return str;
  return str.replace(/\x00/g, '').replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '').trim();
}

async function cleanNullBytesInReturns() {
  try {
    console.log('بدء تنظيف null bytes من جداول المرتجعات...');

    // تنظيف جدول returns
    const returns = await prisma.return.findMany();
    let cleanedReturns = 0;

    for (const returnRecord of returns) {
      const updates = {};
      let needsUpdate = false;

      // فحص وتنظيف كل حقل نصي
      const fieldsToClean = [
        'roNumber',
        'opReturnNumber',
        'soNumber',
        'clientName',
        'warehouseName',
        'notes',
        'status',
        'processedBy',
        'employeeName',
        'attachments'
      ];

      for (const field of fieldsToClean) {
        if (returnRecord[field] && typeof returnRecord[field] === 'string') {
          const cleaned = sanitizeString(returnRecord[field]);
          if (cleaned !== returnRecord[field]) {
            updates[field] = cleaned;
            needsUpdate = true;
            console.log(`تنظيف ${field} في المرتجع ${returnRecord.id}: "${returnRecord[field]}" -> "${cleaned}"`);
          }
        }
      }

      if (needsUpdate) {
        await prisma.return.update({
          where: { id: returnRecord.id },
          data: updates
        });
        cleanedReturns++;
      }
    }

    // تنظيف جدول return items
    const returnItems = await prisma.returnItem.findMany();
    let cleanedReturnItems = 0;

    for (const item of returnItems) {
      const updates = {};
      let needsUpdate = false;

      // فحص وتنظيف كل حقل نصي
      const fieldsToClean = [
        'deviceId',
        'model',
        'returnReason',
        'replacementDeviceId',
        'originalDeviceId'
      ];

      for (const field of fieldsToClean) {
        if (item[field] && typeof item[field] === 'string') {
          const cleaned = sanitizeString(item[field]);
          if (cleaned !== item[field]) {
            updates[field] = cleaned;
            needsUpdate = true;
            console.log(`تنظيف ${field} في عنصر المرتجع ${item.id}: "${item[field]}" -> "${cleaned}"`);
          }
        }
      }

      if (needsUpdate) {
        await prisma.returnItem.update({
          where: { id: item.id },
          data: updates
        });
        cleanedReturnItems++;
      }
    }

    console.log(`\nنتائج التنظيف:`);
    console.log(`المرتجعات المنظفة: ${cleanedReturns}`);
    console.log(`عناصر المرتجعات المنظفة: ${cleanedReturnItems}`);
    console.log('تم الانتهاء من تنظيف null bytes بنجاح!');

  } catch (error) {
    console.error('خطأ في تنظيف null bytes:', error);
  } finally {
    await prisma.$disconnect();
  }
}

cleanNullBytesInReturns();
