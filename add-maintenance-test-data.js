import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function addMaintenanceTestData() {
  try {
    console.log('إضافة بيانات تجريبية للصيانة...');

    // إضافة سجل صيانة تجريبي
    await prisma.maintenanceLog.create({
      data: {
        deviceId: '222222222225555',
        model: 'Samsung Galaxy',
        repairDate: new Date('2024-01-15'),
        notes: 'إصلاح الشاشة المكسورة',
        result: 'Repaired',
        status: 'acknowledged',
        acknowledgedDate: new Date('2024-01-16'),
        warehouseName: 'المخزن الرئيسي',
        acknowledgedBy: 'أحمد محمد'
      }
    });

    // إضافة أمر صيانة تجريبي
    await prisma.maintenanceOrder.create({
      data: {
        orderNumber: 'MAINT-001',
        referenceNumber: 'REF-001',
        date: new Date('2024-01-10'),
        employeeName: 'سارة أحمد',
        maintenanceEmployeeName: 'فني الصيانة',
        notes: 'أمر صيانة للجهاز',
        status: 'completed',
        source: 'warehouse',
        items: JSON.stringify([{
          deviceId: '222222222225555',
          fault: 'شاشة مكسورة',
          damageType: 'تلف فيزيائي',
          notes: 'الشاشة تحتاج استبدال'
        }])
      }
    });

    // إضافة إيصال صيانة تجريبي
    await prisma.maintenanceReceiptOrder.create({
      data: {
        receiptNumber: 'REC-001',
        referenceNumber: 'REF-001',
        date: new Date('2024-01-16'),
        employeeName: 'علي حسن',
        maintenanceEmployeeName: 'فني الصيانة',
        notes: 'استلام الجهاز بعد الصيانة',
        status: 'completed',
        items: JSON.stringify([{
          deviceId: '222222222225555',
          result: 'Repaired',
          notes: 'تم إصلاح الشاشة بنجاح'
        }])
      }
    });

    // إضافة أمر تسليم تجريبي
    await prisma.deliveryOrder.create({
      data: {
        deliveryOrderNumber: 'DEL-001',
        referenceNumber: 'REF-001',
        date: new Date('2024-01-17'),
        warehouseId: 1,
        warehouseName: 'المخزن الرئيسي',
        employeeName: 'محمد أحمد',
        notes: 'تسليم الجهاز للعميل',
        status: 'completed'
      }
    });

    console.log('تم إضافة البيانات التجريبية للصيانة بنجاح');

  } catch (error) {
    console.error('خطأ في إضافة البيانات التجريبية:', error);
  } finally {
    await prisma.$disconnect();
  }
}

addMaintenanceTestData();
