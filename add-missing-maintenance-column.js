#!/usr/bin/env node

/**
 * سكريبت لإضافة العمود المفقود maintenanceEmployeeName إلى جدول MaintenanceReceiptOrder
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function addMissingColumn() {
  console.log('🔧 إضافة العمود المفقود maintenanceEmployeeName...\n');

  try {
    // فحص وجود العمود
    console.log('🔍 فحص وجود العمود maintenanceEmployeeName:');
    
    const columnCheckQuery = `
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'MaintenanceReceiptOrder' 
      AND column_name = 'maintenanceEmployeeName';
    `;

    const columnExists = await prisma.$queryRawUnsafe(columnCheckQuery);
    
    if (columnExists.length > 0) {
      console.log('   ✅ العمود maintenanceEmployeeName موجود بالفعل');
      return;
    }

    console.log('   ⚠️  العمود maintenanceEmployeeName مفقود - سيتم إضافته');

    // إضافة العمود
    const addColumnQuery = `
      ALTER TABLE "MaintenanceReceiptOrder" 
      ADD COLUMN "maintenanceEmployeeName" TEXT;
    `;

    await prisma.$executeRawUnsafe(addColumnQuery);
    console.log('   ✅ تم إضافة العمود maintenanceEmployeeName بنجاح');

    // فحص أعمدة الجدول بعد الإضافة
    console.log('\n📋 أعمدة جدول MaintenanceReceiptOrder بعد التحديث:');
    
    const columnsQuery = `
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'MaintenanceReceiptOrder'
      ORDER BY ordinal_position;
    `;

    const columns = await prisma.$queryRawUnsafe(columnsQuery);
    columns.forEach(col => {
      console.log(`   • ${col.column_name} (${col.data_type}) - ${col.is_nullable === 'YES' ? 'NULL' : 'NOT NULL'}`);
    });

    // اختبار عملية جلب البيانات
    console.log('\n🧪 اختبار جلب بيانات MaintenanceReceiptOrder:');
    
    const maintenanceReceipts = await prisma.maintenanceReceiptOrder.findMany({
      take: 5
    });
    
    console.log(`   ✅ تم جلب ${maintenanceReceipts.length} سجل بنجاح`);

    console.log('\n✅ تم إكمال إضافة العمود بنجاح!');

  } catch (error) {
    console.error('❌ خطأ أثناء إضافة العمود:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// تشغيل السكريبت
if (require.main === module) {
  addMissingColumn()
    .then(() => {
      console.log('\n🎉 تم إنجاز إضافة العمود بنجاح!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 فشل إضافة العمود:', error);
      process.exit(1);
    });
}

module.exports = { addMissingColumn };
