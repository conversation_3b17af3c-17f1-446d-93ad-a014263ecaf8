#!/usr/bin/env node

/**
 * سكريبت لتنظيف البيانات التالفة في قاعدة البيانات
 * يزيل null bytes والبيانات غير الصالحة
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function cleanDatabaseNullBytes() {
  console.log('🧹 بدء تنظيف البيانات التالفة في قاعدة البيانات...\n');

  try {
    // تنظيف جدول maintenanceOrder
    console.log('📋 تنظيف جدول maintenanceOrder:');
    
    // استخدام raw query لتنظيف null bytes
    const cleanMaintenanceQuery = `
      UPDATE "MaintenanceOrder" 
      SET 
        "orderNumber" = REPLACE("orderNumber", E'\\x00', ''),
        "referenceNumber" = REPLACE(COALESCE("referenceNumber", ''), E'\\x00', ''),
        "employeeName" = REPLACE("employeeName", E'\\x00', ''),
        "maintenanceEmployeeName" = REPLACE(COALESCE("maintenanceEmployeeName", ''), E'\\x00', ''),
        "items" = REPLACE(COALESCE("items", ''), E'\\x00', ''),
        "notes" = REPLACE(COALESCE("notes", ''), E'\\x00', ''),
        "attachmentName" = REPLACE(COALESCE("attachmentName", ''), E'\\x00', '')
      WHERE 
        "orderNumber" LIKE '%' || E'\\x00' || '%' OR
        "referenceNumber" LIKE '%' || E'\\x00' || '%' OR
        "employeeName" LIKE '%' || E'\\x00' || '%' OR
        "maintenanceEmployeeName" LIKE '%' || E'\\x00' || '%' OR
        "items" LIKE '%' || E'\\x00' || '%' OR
        "notes" LIKE '%' || E'\\x00' || '%' OR
        "attachmentName" LIKE '%' || E'\\x00' || '%';
    `;

    const maintenanceResult = await prisma.$executeRawUnsafe(cleanMaintenanceQuery);
    console.log(`   ✅ تم تنظيف ${maintenanceResult} سجل في maintenanceOrder`);

    // تنظيف جدول Device
    console.log('\n📱 تنظيف جدول Device:');
    
    const cleanDeviceQuery = `
      UPDATE "Device" 
      SET 
        "id" = REPLACE("id", E'\\x00', ''),
        "model" = REPLACE("model", E'\\x00', ''),
        "status" = REPLACE("status", E'\\x00', ''),
        "storage" = REPLACE("storage", E'\\x00', ''),
        "condition" = REPLACE("condition", E'\\x00', '')
      WHERE 
        "id" LIKE '%' || E'\\x00' || '%' OR
        "model" LIKE '%' || E'\\x00' || '%' OR
        "status" LIKE '%' || E'\\x00' || '%' OR
        "storage" LIKE '%' || E'\\x00' || '%' OR
        "condition" LIKE '%' || E'\\x00' || '%';
    `;

    const deviceResult = await prisma.$executeRawUnsafe(cleanDeviceQuery);
    console.log(`   ✅ تم تنظيف ${deviceResult} سجل في Device`);

    // تنظيف جدول MaintenanceOrderItem
    console.log('\n🔧 تنظيف جدول MaintenanceOrderItem:');
    
    const cleanMaintenanceItemQuery = `
      UPDATE "MaintenanceOrderItem" 
      SET 
        "deviceId" = REPLACE("deviceId", E'\\x00', ''),
        "model" = REPLACE("model", E'\\x00', ''),
        "fault" = REPLACE(COALESCE("fault", ''), E'\\x00', ''),
        "notes" = REPLACE(COALESCE("notes", ''), E'\\x00', '')
      WHERE 
        "deviceId" LIKE '%' || E'\\x00' || '%' OR
        "model" LIKE '%' || E'\\x00' || '%' OR
        "fault" LIKE '%' || E'\\x00' || '%' OR
        "notes" LIKE '%' || E'\\x00' || '%';
    `;

    const maintenanceItemResult = await prisma.$executeRawUnsafe(cleanMaintenanceItemQuery);
    console.log(`   ✅ تم تنظيف ${maintenanceItemResult} سجل في MaintenanceOrderItem`);

    // تنظيف جدول User إذا وجد
    console.log('\n👤 تنظيف جدول User:');
    
    try {
      const cleanUserQuery = `
        UPDATE "User" 
        SET 
          "username" = REPLACE("username", E'\\x00', ''),
          "name" = REPLACE(COALESCE("name", ''), E'\\x00', ''),
          "email" = REPLACE(COALESCE("email", ''), E'\\x00', '')
        WHERE 
          "username" LIKE '%' || E'\\x00' || '%' OR
          "name" LIKE '%' || E'\\x00' || '%' OR
          "email" LIKE '%' || E'\\x00' || '%';
      `;

      const userResult = await prisma.$executeRawUnsafe(cleanUserQuery);
      console.log(`   ✅ تم تنظيف ${userResult} سجل في User`);
    } catch (error) {
      console.log(`   ⚠️  جدول User غير موجود أو خطأ في التنظيف`);
    }

    // إعادة فحص البيانات بعد التنظيف
    console.log('\n🔍 فحص البيانات بعد التنظيف:');
    
    const maintenanceOrders = await prisma.maintenanceOrder.findMany();
    console.log(`   ✅ تم جلب ${maintenanceOrders.length} أمر صيانة بنجاح`);

    const devices = await prisma.device.findMany();
    console.log(`   ✅ تم جلب ${devices.length} جهاز بنجاح`);

    // اختبار إنشاء سجل جديد
    console.log('\n🧪 اختبار إنشاء أمر صيانة جديد:');
    
    const testOrder = await prisma.maintenanceOrder.create({
      data: {
        orderNumber: `CLEAN-TEST-${Date.now()}`,
        date: new Date().toISOString(),
        employeeName: 'موظف اختبار التنظيف',
        items: JSON.stringify([]),
        status: 'wip',
        source: 'warehouse'
      }
    });

    console.log(`   ✅ تم إنشاء أمر اختبار: ${testOrder.orderNumber}`);

    // حذف أمر الاختبار
    await prisma.maintenanceOrder.delete({
      where: { id: testOrder.id }
    });
    console.log('   🧹 تم حذف أمر الاختبار');

    console.log('\n✅ تم إكمال تنظيف قاعدة البيانات بنجاح!');

  } catch (error) {
    console.error('❌ خطأ أثناء تنظيف قاعدة البيانات:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// تشغيل السكريبت
if (require.main === module) {
  cleanDatabaseNullBytes()
    .then(() => {
      console.log('\n🎉 تم إنجاز تنظيف قاعدة البيانات بنجاح!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 فشل تنظيف قاعدة البيانات:', error);
      process.exit(1);
    });
}

module.exports = { cleanDatabaseNullBytes };
