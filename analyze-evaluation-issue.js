const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function analyzeEvaluationIssue() {
  try {
    console.log('🔍 تحليل مشكلة أوامر التقييم...\n');

    // جلب جميع أوامر التقييم
    const evaluationOrders = await prisma.evaluationOrder.findMany({
      include: {
        items: {
          select: {
            deviceId: true,
            id: true
          }
        }
      },
      orderBy: {
        date: 'desc'
      }
    });

    console.log(`📊 أوامر التقييم المتوفرة: ${evaluationOrders.length}\n`);

    evaluationOrders.forEach(order => {
      console.log(`📝 أمر التقييم ${order.orderId}:`);
      console.log(`   التاريخ: ${order.date} (${new Date(order.date).toISOString()})`);
      console.log(`   الحالة: ${order.status}`);
      console.log(`   عدد الأجهزة: ${order.items?.length || 0}`);
      
      if (order.items && order.items.length > 0) {
        const deviceIds = order.items.map(item => item.deviceId || item.id);
        console.log(`   الأجهزة: [${deviceIds.join(', ')}]`);
      }
      console.log('');
    });

    // جلب جميع أوامر الصيانة (إذا وجدت)
    const maintenanceOrders = await prisma.maintenanceOrder.findMany({
      select: {
        id: true,
        orderNumber: true,
        date: true,
        status: true,
        items: true
      },
      orderBy: {
        date: 'desc'
      }
    });

    console.log(`🔧 أوامر الصيانة المتوفرة: ${maintenanceOrders.length}\n`);

    if (maintenanceOrders.length > 0) {
      maintenanceOrders.forEach(order => {
        console.log(`🔧 أمر الصيانة ${order.orderNumber}:`);
        console.log(`   التاريخ: ${order.date} (${new Date(order.date).toISOString()})`);
        console.log(`   الحالة: ${order.status}`);
        
        try {
          const items = order.items ? JSON.parse(order.items) : [];
          console.log(`   عدد الأجهزة: ${items.length}`);
          
          if (items.length > 0) {
            const deviceIds = items.map(item => item.deviceId || item.id).filter(Boolean);
            console.log(`   الأجهزة: [${deviceIds.join(', ')}]`);
            
            // فحص التداخل مع أوامر التقييم
            for (const evalOrder of evaluationOrders) {
              const evalDeviceIds = evalOrder.items ? evalOrder.items.map(item => item.deviceId || item.id) : [];
              const sharedDevices = deviceIds.filter(deviceId => evalDeviceIds.includes(deviceId));
              
              if (sharedDevices.length > 0) {
                const maintenanceDate = new Date(order.date);
                const evalDate = new Date(evalOrder.date);
                
                console.log(`   🔗 تداخل مع أمر التقييم ${evalOrder.orderId}:`);
                console.log(`      الأجهزة المشتركة: [${sharedDevices.join(', ')}]`);
                console.log(`      التقييم ${evalDate > maintenanceDate ? 'بعد' : 'قبل'} الصيانة`);
                console.log(`      الفرق: ${Math.abs(evalDate.getTime() - maintenanceDate.getTime()) / (1000 * 60)} دقيقة`);
              }
            }
          }
        } catch (error) {
          console.log(`   ❌ خطأ في تحليل items: ${error.message}`);
        }
        console.log('');
      });
    }

    // تحليل المنطق المطلوب
    console.log('🎯 تحليل المنطق:');
    console.log('================');
    console.log('المشكلة: النظام يمنع حذف/استلام أوامر الصيانة بسبب أوامر التقييم');
    console.log('الحل المطلوب: السماح بحذف/استلام أوامر الصيانة بغض النظر عن أوامر التقييم');
    console.log('المنطق الصحيح: أوامر التقييم لا يجب أن تمنع عمليات الصيانة');

  } catch (error) {
    console.error('خطأ:', error);
  } finally {
    await prisma.$disconnect();
  }
}

analyzeEvaluationIssue();
