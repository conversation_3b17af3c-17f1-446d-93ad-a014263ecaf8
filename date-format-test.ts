/**
 * اختبار توحيد تنسيقات التاريخ والوقت
 * Date/Time Format Standardization Test
 * تاريخ: 4 أغسطس 2025
 */

import { 
  formatDate, 
  formatDateTime, 
  formatTime, 
  formatTableDate, 
  formatDateForCSV,
  getRelativeTimeArabic,
  isToday,
  getCurrentArabicDate 
} from '@/lib/date-utils';

console.log('🕐 اختبار توحيد تنسيقات التاريخ والوقت...\n');

// تاريخ اختبار
const testDate = new Date('2024-08-05T14:30:00');
const today = new Date();

console.log('📅 اختبار الدوال الأساسية:');
console.log('==========================');

// formatDate
console.log('1. formatDate() - التاريخ فقط:');
console.log(`   • عادي: ${formatDate(testDate)}`);
console.log(`   • عربي: ${formatDate(testDate, { arabic: true })}`);
console.log(`   • مختصر: ${formatDate(testDate, { format: 'short' })}`);
console.log(`   • جدول: ${formatDate(testDate, { format: 'table' })}`);

// formatDateTime
console.log('\n2. formatDateTime() - التاريخ والوقت:');
console.log(`   • عادي: ${formatDateTime(testDate)}`);
console.log(`   • عربي: ${formatDateTime(testDate, { arabic: true })}`);
console.log(`   • مختصر: ${formatDateTime(testDate, { format: 'short' })}`);

// formatTime
console.log('\n3. formatTime() - الوقت فقط:');
console.log(`   • 24 ساعة: ${formatTime(testDate)}`);
console.log(`   • 12 ساعة: ${formatTime(testDate, { format12h: true })}`);

console.log('\n📊 اختبار دوال متخصصة:');
console.log('==========================');

// formatTableDate
console.log('4. formatTableDate() - للجداول:');
console.log(`   • مع الوقت: ${formatTableDate(testDate, true)}`);
console.log(`   • بدون وقت: ${formatTableDate(testDate, false)}`);

// formatDateForCSV
console.log('\n5. formatDateForCSV() - للتصدير:');
console.log(`   • CSV: ${formatDateForCSV(testDate)}`);

// getRelativeTimeArabic
console.log('\n6. getRelativeTimeArabic() - الوقت النسبي:');
console.log(`   • قبل ساعة: ${getRelativeTimeArabic(new Date(Date.now() - 3600000))}`);
console.log(`   • تاريخ قديم: ${getRelativeTimeArabic(testDate)}`);

// isToday
console.log('\n7. isToday() - فحص التاريخ:');
console.log(`   • اليوم: ${isToday(today)}`);
console.log(`   • تاريخ آخر: ${isToday(testDate)}`);

// getCurrentArabicDate
console.log('\n8. getCurrentArabicDate() - التاريخ الحالي:');
console.log(`   • الآن: ${getCurrentArabicDate()}`);

console.log('\n🔧 اختبار معالجة القيم الفارغة:');
console.log('==================================');

// null/undefined handling
console.log('9. معالجة القيم الفارغة:');
console.log(`   • null: ${formatDate(null)}`);
console.log(`   • undefined: ${formatDateTime(undefined)}`);
console.log(`   • نص فارغ: ${formatTime('')}`);
console.log(`   • تاريخ غير صحيح: ${formatDate('invalid-date')}`);

console.log('\n📋 ملخص الفوائد:');
console.log('================');
console.log('✅ توحيد تنسيق التاريخ والوقت في جميع أنحاء التطبيق');
console.log('✅ دعم اللغة العربية والإنجليزية');
console.log('✅ معالجة آمنة للقيم الفارغة والتواريخ غير الصحيحة');
console.log('✅ دوال متخصصة للجداول والتصدير');
console.log('✅ استخدام مكتبة date-fns للاستقرار والأداء');
console.log('✅ تجربة مستخدم متسقة');

console.log('\n📝 الملفات المحدثة:');
console.log('==================');
const updatedFiles = [
  'lib/date-utils.ts - تحسين وإضافة دوال جديدة',
  'app/(main)/track/page.tsx - استبدال formatArabicDate',
  'app/(main)/inventory/page.tsx - استبدال toLocaleDateString',
  'app/(main)/stocktaking/page.tsx - توحيد تنسيقات متعددة',
  'lib/template-service.ts - تحديث القوالب',
  'lib/print-templates/index.ts - تحديث دوال الطباعة',
  'lib/pdf-utils.ts - توحيد تنسيقات PDF',
  'lib/export-utils/html-to-pdf.ts - تحديث التصدير',
  'lib/export-utils/enhanced-html-export.ts - تحسين العربية'
];

updatedFiles.forEach((file, index) => {
  console.log(`${index + 1}. ${file}`);
});

console.log('\n✨ اكتمل توحيد تنسيقات التاريخ والوقت بنجاح!');

export {};
