const fs = require('fs');
const path = require('path');

function searchForParams(dir) {
  const issues = [];
  
  function scanDirectory(dirPath) {
    const files = fs.readdirSync(dirPath);
    
    for (const file of files) {
      const fullPath = path.join(dirPath, file);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        scanDirectory(fullPath);
      } else if (file === 'route.ts' && fullPath.includes('[id]')) {
        const content = fs.readFileSync(fullPath, 'utf8');
        
        // البحث عن استخدام params.xxx بدون await
        const paramsUsageRegex = /params\.(\w+)/g;
        const awaitParamsRegex = /await\s+params/;
        
        const paramsMatches = content.match(paramsUsageRegex);
        const hasAwaitParams = awaitParamsRegex.test(content);
        
        if (paramsMatches && !hasAwaitParams) {
          issues.push({
            file: fullPath,
            matches: paramsMatches,
            needsAwait: true
          });
        } else if (paramsMatches && hasAwaitParams) {
          // فحص ما إذا كان استخدام params بعد await
          const lines = content.split('\n');
          let awaitLine = -1;
          let problemLines = [];
          
          lines.forEach((line, index) => {
            if (line.includes('await params')) {
              awaitLine = index;
            }
            if (line.includes('params.') && !line.includes('resolvedParams') && index < awaitLine) {
              problemLines.push(index + 1);
            }
          });
          
          if (problemLines.length > 0) {
            issues.push({
              file: fullPath,
              matches: paramsMatches,
              problemLines: problemLines,
              needsReorder: true
            });
          }
        }
      }
    }
  }
  
  scanDirectory(dir);
  return issues;
}

console.log('🔍 فحص ملفات API routes للمشاكل المرتبطة بـ params...\n');

const apiDir = path.join(__dirname, 'app', 'api');
const issues = searchForParams(apiDir);

if (issues.length === 0) {
  console.log('✅ لم يتم العثور على مشاكل في استخدام params');
} else {
  console.log(`❌ تم العثور على ${issues.length} ملف(ات) بها مشاكل:\n`);
  
  issues.forEach((issue, index) => {
    console.log(`${index + 1}. ${issue.file.replace(__dirname + '\\', '')}`);
    console.log(`   المشاكل: ${issue.matches.join(', ')}`);
    
    if (issue.needsAwait) {
      console.log(`   الحل: إضافة await params قبل الاستخدام`);
    }
    
    if (issue.problemLines) {
      console.log(`   الأسطر المشكلة: ${issue.problemLines.join(', ')}`);
    }
    
    console.log('');
  });
  
  console.log('💡 الحل المطلوب:');
  console.log('1. إضافة: const resolvedParams = await params;');
  console.log('2. استبدال: params.id بـ resolvedParams.id');
  console.log('3. وضع await params قبل أي استخدام لـ params');
}

console.log('\n📊 ملاحظة: هذا التغيير مطلوب في Next.js 15 للـ dynamic route parameters');
