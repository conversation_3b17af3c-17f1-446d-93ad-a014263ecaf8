import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET - جلب جميع التحويلات المخزنية
export async function GET() {
  try {
    const records = await prisma.warehouseTransfer.findMany({
      orderBy: { createdAt: 'desc' }
    });

    return NextResponse.json(records);
  } catch (error) {
    console.error('خطأ في جلب API التحويلات المخزنية:', error);
    return NextResponse.json(
      { error: 'فشل في جلب API التحويلات المخزنية' },
      { status: 500 }
    );
  }
}

// POST - إنشاء API التحويلات المخزنية جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    const record = await prisma.warehouseTransfer.create({
      data: body
    });

    return NextResponse.json(record, { status: 201 });
  } catch (error) {
    console.error('خطأ في إنشاء API التحويلات المخزنية:', error);
    return NextResponse.json(
      { error: 'فشل في إنشاء API التحويلات المخزنية' },
      { status: 500 }
    );
  }
}

// PUT - تحديث API التحويلات المخزنية
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, ...data } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'معرف السجل مطلوب' },
        { status: 400 }
      );
    }

    const record = await prisma.warehouseTransfer.update({
      where: { id: parseInt(id) },
      data
    });

    return NextResponse.json(record);
  } catch (error) {
    console.error('خطأ في تحديث API التحويلات المخزنية:', error);
    return NextResponse.json(
      { error: 'فشل في تحديث API التحويلات المخزنية' },
      { status: 500 }
    );
  }
}

// DELETE - حذف API التحويلات المخزنية
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'معرف السجل مطلوب' },
        { status: 400 }
      );
    }

    await prisma.warehouseTransfer.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({ message: 'تم حذف السجل بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف API التحويلات المخزنية:', error);
    return NextResponse.json(
      { error: 'فشل في حذف السجل' },
      { status: 500 }
    );
  }
}