const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixEvaluationTableDateTypes() {
  try {
    console.log('🔧 Fixing evaluation_orders table date column types...');

    // تحويل عمود date من text إلى timestamp
    console.log('1️⃣ Converting date column to timestamp...');
    await prisma.$executeRaw`
      ALTER TABLE evaluation_orders 
      ALTER COLUMN date TYPE TIMESTAMP USING date::timestamp
    `;

    // تحويل عمود acknowledgedDate من text إلى timestamp (nullable)
    console.log('2️⃣ Converting acknowledgedDate column to timestamp...');
    await prisma.$executeRaw`
      ALTER TABLE evaluation_orders 
      ALTER COLUMN "acknowledgedDate" TYPE TIMESTAMP USING 
      CASE 
        WHEN "acknowledgedDate" IS NULL OR "acknowledgedDate" = '' THEN NULL 
        ELSE "acknowledgedDate"::timestamp 
      END
    `;

    console.log('✅ Successfully fixed date column types!');

    // اختبار إنشاء evaluation order جديد
    console.log('\n🧪 Testing evaluation order creation...');
    const testData = {
      orderId: 'TEST-FIXED-001',
      employeeName: 'System Administrator',
      date: new Date().toISOString(),
      status: 'معلق',
      notes: null,
      acknowledgedBy: null,
      acknowledgedDate: null,
      warehouseName: null
    };

    const testEval = await prisma.evaluationOrder.create({
      data: testData
    });

    console.log('✅ Test creation successful!', testEval.id);

    // تنظيف
    await prisma.evaluationOrder.delete({ where: { id: testEval.id } });

  } catch (error) {
    console.error('❌ Fix failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixEvaluationTableDateTypes();
