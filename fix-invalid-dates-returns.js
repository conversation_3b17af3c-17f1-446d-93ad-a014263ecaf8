const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixInvalidDates() {
  try {
    console.log('🔍 فحص التواريخ غير الصالحة في جدول المرتجعات...');

    // البحث عن المرتجعات مع التواريخ غير الصالحة
    const returns = await prisma.$queryRaw`
      SELECT id, "roNumber", date, "processedDate", "createdAt"
      FROM "Return" 
      WHERE date IS NULL 
         OR "createdAt" IS NULL
         OR CAST(date as TEXT) = 'Invalid Date'
         OR CAST("processedDate" as TEXT) = 'Invalid Date'
    `;

    console.log(`📊 تم العثور على ${returns.length} مرتجع مع تواريخ غير صالحة`);

    if (returns.length === 0) {
      console.log('✅ لم يتم العثور على تواريخ غير صالحة');
      return;
    }

    // عرض المرتجعات المتأثرة
    console.log('\n📋 المرتجعات المتأثرة:');
    returns.forEach((ret, index) => {
      console.log(`${index + 1}. ID: ${ret.id}, RO: ${ret.roNumber}`);
      console.log(`   التاريخ: ${ret.date}`);
      console.log(`   تاريخ المعالجة: ${ret.processedDate}`);
      console.log(`   تاريخ الإنشاء: ${ret.createdAt}`);
      console.log('');
    });

    // إصلاح التواريخ غير الصالحة
    console.log('🔧 إصلاح التواريخ غير الصالحة...');
    
    for (const ret of returns) {
      const currentDate = new Date();
      
      await prisma.$executeRaw`
        UPDATE "Return" 
        SET 
          date = CASE 
            WHEN date IS NULL OR CAST(date as TEXT) = 'Invalid Date'
            THEN ${currentDate}::timestamp 
            ELSE date 
          END,
          "processedDate" = CASE 
            WHEN CAST("processedDate" as TEXT) = 'Invalid Date'
            THEN NULL 
            ELSE "processedDate" 
          END,
          "createdAt" = CASE 
            WHEN "createdAt" IS NULL 
            THEN ${currentDate}::timestamp 
            ELSE "createdAt" 
          END
        WHERE id = ${ret.id}
      `;
      
      console.log(`✅ تم إصلاح المرتجع: ${ret.roNumber}`);
    }

    console.log('\n🎉 تم إصلاح جميع التواريخ غير الصالحة بنجاح!');

    // التحقق من النتائج
    const updatedReturns = await prisma.$queryRaw`
      SELECT id, "roNumber", date, "processedDate", "createdAt"
      FROM "Return" 
      WHERE date IS NULL 
         OR "createdAt" IS NULL
         OR CAST(date as TEXT) = 'Invalid Date'
         OR CAST("processedDate" as TEXT) = 'Invalid Date'
    `;

    console.log(`\n📊 التحقق النهائي: ${updatedReturns.length} مرتجع مع تواريخ غير صالحة متبقية`);

  } catch (error) {
    console.error('❌ خطأ في إصلاح التواريخ:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixInvalidDates();
