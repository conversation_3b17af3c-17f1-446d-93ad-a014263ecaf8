const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function deleteMaintenanceOrder() {
  try {
    console.log('🗑️ حذف أمر الصيانة المتبقي...\n');

    // العثور على الأمر
    const order = await prisma.maintenanceOrder.findFirst({
      where: { orderNumber: 'MTRANS-1' },
      select: {
        id: true,
        orderNumber: true,
        status: true,
        items: true
      }
    });

    if (!order) {
      console.log('❌ أمر الصيانة غير موجود');
      return;
    }

    console.log(`🔧 تم العثور على أمر الصيانة:`);
    console.log(`   رقم الأمر: ${order.orderNumber}`);
    console.log(`   ID: ${order.id}`);
    console.log(`   الحالة: ${order.status}`);

    // تحليل الأجهزة
    let devices = [];
    try {
      devices = order.items ? JSON.parse(order.items) : [];
    } catch (error) {
      console.log(`❌ خطأ في تحليل items: ${error.message}`);
    }

    console.log(`   عدد الأجهزة: ${devices.length}`);

    // حذف الأمر
    await prisma.maintenanceOrder.delete({
      where: { id: order.id }
    });

    console.log(`✅ تم حذف أمر الصيانة ${order.orderNumber} بنجاح`);

    // التحقق من حذف الأمر
    const checkOrder = await prisma.maintenanceOrder.findFirst({
      where: { orderNumber: 'MTRANS-1' }
    });

    if (!checkOrder) {
      console.log(`✅ تأكيد: الأمر لم يعد موجوداً في قاعدة البيانات`);
    } else {
      console.log(`❌ خطأ: الأمر لا يزال موجوداً!`);
    }

    // التحقق من حالة الأجهزة
    if (devices.length > 0) {
      console.log(`\n📱 فحص حالة الأجهزة بعد الحذف:`);
      for (const item of devices) {
        const deviceId = item.deviceId || item.id;
        if (deviceId) {
          const device = await prisma.device.findUnique({
            where: { id: deviceId },
            select: { id: true, model: true, status: true }
          });

          if (device) {
            console.log(`   ${device.id} - ${device.model} - حالة: ${device.status}`);
          } else {
            console.log(`   ❌ جهاز ${deviceId} غير موجود`);
          }
        }
      }
    }

    console.log(`\n🎉 تم الانتهاء من التنظيف. الآن:`);
    console.log(`   ✅ لا توجد أوامر صيانة متبقية`);
    console.log(`   ✅ الأجهزة متاحة للبيع`);
    console.log(`   ✅ يمكن إرسال الأجهزة للصيانة مرة أخرى`);

  } catch (error) {
    console.error('خطأ:', error);
  } finally {
    await prisma.$disconnect();
  }
}

deleteMaintenanceOrder();
